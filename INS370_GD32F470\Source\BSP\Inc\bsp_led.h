/**
  ******************************************************************************
  * @file    bsp_led.h
  * <AUTHOR> Team
  * @brief   Header file for LED BSP module
  * @version V1.0.0
  * @date    2024-12-19
  ******************************************************************************
  */

#ifndef __BSP_LED_H
#define __BSP_LED_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "hal_gpio.h"
#include "hardware_config.h"

/* Exported types ------------------------------------------------------------*/
typedef enum {
    LED1 = 0,   // Power LED
    LED2,       // Status LED
    LED3,       // Error LED
    LED_COUNT
} bsp_led_t;

typedef enum {
    LED_MODE_OFF = 0,
    LED_MODE_ON,
    LED_MODE_BLINK_SLOW,
    LED_MODE_BLINK_FAST,
    LED_MODE_PULSE
} bsp_led_mode_t;

/* Exported constants --------------------------------------------------------*/
#define LED_BLINK_SLOW_PERIOD   1000    // ms
#define LED_BLINK_FAST_PERIOD   200     // ms
#define LED_PULSE_PERIOD        100     // ms

/* Exported functions --------------------------------------------------------*/

/* Initialization functions */
void BSP_LED_Init(void);
void BSP_LED_DeInit(void);

/* Basic LED control */
void BSP_LED_On(bsp_led_t led);
void BSP_LED_Off(bsp_led_t led);
void BSP_LED_Toggle(bsp_led_t led);

/* Advanced LED control */
void BSP_LED_SetMode(bsp_led_t led, bsp_led_mode_t mode);
void BSP_LED_Update(void);  // Call this periodically for blinking modes

/* Utility functions */
void BSP_LED_AllOn(void);
void BSP_LED_AllOff(void);
void BSP_LED_Test(void);

#ifdef __cplusplus
}
#endif

#endif /* __BSP_LED_H */

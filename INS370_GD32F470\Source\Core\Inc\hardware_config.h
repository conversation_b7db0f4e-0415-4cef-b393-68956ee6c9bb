/**
  ******************************************************************************
  * @file    hardware_config.h
  * <AUTHOR> Team
  * @brief   Hardware pin and peripheral configuration
  * @version V1.0.0
  * @date    2024-12-19
  ******************************************************************************
  */

#ifndef __HARDWARE_CONFIG_H
#define __HARDWARE_CONFIG_H

#ifdef __cplusplus
extern "C" {
#endif

#include "gd32f4xx.h"

/* GPIO Pin Definitions ------------------------------------------------------*/

/* LED Configuration */
#define LED1_PORT               GPIOC
#define LED1_PIN                GPIO_PIN_13
#define LED1_CLK                RCU_GPIOC

#define LED2_PORT               GPIOC
#define LED2_PIN                GPIO_PIN_14
#define LED2_CLK                RCU_GPIOC

#define LED3_PORT               GPIOC
#define LED3_PIN                GPIO_PIN_15
#define LED3_CLK                RCU_GPIOC

/* Button Configuration */
#define BUTTON1_PORT            GPIOA
#define BUTTON1_PIN             GPIO_PIN_0
#define BUTTON1_CLK             RCU_GPIOA
#define BUTTON1_EXTI_LINE       EXTI_0
#define BUTTON1_EXTI_PORT       EXTI_SOURCE_GPIOA
#define BUTTON1_EXTI_PIN        EXTI_SOURCE_PIN0
#define BUTTON1_EXTI_IRQ        EXTI0_IRQn

/* UART Configuration --------------------------------------------------------*/

/* UART1 - Debug/Console */
#define UART1_PERIPH            USART0
#define UART1_CLK               RCU_USART0
#define UART1_TX_PORT           GPIOA
#define UART1_TX_PIN            GPIO_PIN_9
#define UART1_TX_AF             GPIO_AF_7
#define UART1_TX_CLK            RCU_GPIOA
#define UART1_RX_PORT           GPIOA
#define UART1_RX_PIN            GPIO_PIN_10
#define UART1_RX_AF             GPIO_AF_7
#define UART1_RX_CLK            RCU_GPIOA
#define UART1_IRQ               USART0_IRQn

/* UART2 - GPS */
#define UART2_PERIPH            USART1
#define UART2_CLK               RCU_USART1
#define UART2_TX_PORT           GPIOA
#define UART2_TX_PIN            GPIO_PIN_2
#define UART2_TX_AF             GPIO_AF_7
#define UART2_TX_CLK            RCU_GPIOA
#define UART2_RX_PORT           GPIOA
#define UART2_RX_PIN            GPIO_PIN_3
#define UART2_RX_AF             GPIO_AF_7
#define UART2_RX_CLK            RCU_GPIOA
#define UART2_IRQ               USART1_IRQn

/* UART3 - External Communication */
#define UART3_PERIPH            USART2
#define UART3_CLK               RCU_USART2
#define UART3_TX_PORT           GPIOB
#define UART3_TX_PIN            GPIO_PIN_10
#define UART3_TX_AF             GPIO_AF_7
#define UART3_TX_CLK            RCU_GPIOB
#define UART3_RX_PORT           GPIOB
#define UART3_RX_PIN            GPIO_PIN_11
#define UART3_RX_AF             GPIO_AF_7
#define UART3_RX_CLK            RCU_GPIOB
#define UART3_IRQ               USART2_IRQn

/* UART4 - Data Output */
#define UART4_PERIPH            UART3
#define UART4_CLK               RCU_UART3
#define UART4_TX_PORT           GPIOC
#define UART4_TX_PIN            GPIO_PIN_10
#define UART4_TX_AF             GPIO_AF_8
#define UART4_TX_CLK            RCU_GPIOC
#define UART4_RX_PORT           GPIOC
#define UART4_RX_PIN            GPIO_PIN_11
#define UART4_RX_AF             GPIO_AF_8
#define UART4_RX_CLK            RCU_GPIOC
#define UART4_IRQ               UART3_IRQn

/* SPI Configuration ---------------------------------------------------------*/

/* SPI1 - IMU Interface */
#define SPI1_PERIPH             SPI0
#define SPI1_CLK                RCU_SPI0
#define SPI1_SCK_PORT           GPIOA
#define SPI1_SCK_PIN            GPIO_PIN_5
#define SPI1_SCK_AF             GPIO_AF_5
#define SPI1_SCK_CLK            RCU_GPIOA
#define SPI1_MISO_PORT          GPIOA
#define SPI1_MISO_PIN           GPIO_PIN_6
#define SPI1_MISO_AF            GPIO_AF_5
#define SPI1_MISO_CLK           RCU_GPIOA
#define SPI1_MOSI_PORT          GPIOA
#define SPI1_MOSI_PIN           GPIO_PIN_7
#define SPI1_MOSI_AF            GPIO_AF_5
#define SPI1_MOSI_CLK           RCU_GPIOA
#define SPI1_CS_PORT            GPIOA
#define SPI1_CS_PIN             GPIO_PIN_4
#define SPI1_CS_CLK             RCU_GPIOA

/* SPI2 - External Sensor */
#define SPI2_PERIPH             SPI1
#define SPI2_CLK                RCU_SPI1
#define SPI2_SCK_PORT           GPIOB
#define SPI2_SCK_PIN            GPIO_PIN_13
#define SPI2_SCK_AF             GPIO_AF_5
#define SPI2_SCK_CLK            RCU_GPIOB
#define SPI2_MISO_PORT          GPIOB
#define SPI2_MISO_PIN           GPIO_PIN_14
#define SPI2_MISO_AF            GPIO_AF_5
#define SPI2_MISO_CLK           RCU_GPIOB
#define SPI2_MOSI_PORT          GPIOB
#define SPI2_MOSI_PIN           GPIO_PIN_15
#define SPI2_MOSI_AF            GPIO_AF_5
#define SPI2_MOSI_CLK           RCU_GPIOB
#define SPI2_CS_PORT            GPIOB
#define SPI2_CS_PIN             GPIO_PIN_12
#define SPI2_CS_CLK             RCU_GPIOB

/* I2C Configuration ---------------------------------------------------------*/

/* I2C1 - Sensor Bus */
#define I2C1_PERIPH             I2C0
#define I2C1_CLK                RCU_I2C0
#define I2C1_SCL_PORT           GPIOB
#define I2C1_SCL_PIN            GPIO_PIN_8
#define I2C1_SCL_AF             GPIO_AF_4
#define I2C1_SCL_CLK            RCU_GPIOB
#define I2C1_SDA_PORT           GPIOB
#define I2C1_SDA_PIN            GPIO_PIN_9
#define I2C1_SDA_AF             GPIO_AF_4
#define I2C1_SDA_CLK            RCU_GPIOB
#define I2C1_IRQ                I2C0_EV_IRQn

/* I2C2 - External Interface */
#define I2C2_PERIPH             I2C1
#define I2C2_CLK                RCU_I2C1
#define I2C2_SCL_PORT           GPIOB
#define I2C2_SCL_PIN            GPIO_PIN_10
#define I2C2_SCL_AF             GPIO_AF_4
#define I2C2_SCL_CLK            RCU_GPIOB
#define I2C2_SDA_PORT           GPIOB
#define I2C2_SDA_PIN            GPIO_PIN_11
#define I2C2_SDA_AF             GPIO_AF_4
#define I2C2_SDA_CLK            RCU_GPIOB
#define I2C2_IRQ                I2C1_EV_IRQn

/* CAN Configuration ---------------------------------------------------------*/

/* CAN1 - Vehicle Interface */
#define CAN1_PERIPH             CAN0
#define CAN1_CLK                RCU_CAN0
#define CAN1_TX_PORT            GPIOD
#define CAN1_TX_PIN             GPIO_PIN_1
#define CAN1_TX_AF              GPIO_AF_9
#define CAN1_TX_CLK             RCU_GPIOD
#define CAN1_RX_PORT            GPIOD
#define CAN1_RX_PIN             GPIO_PIN_0
#define CAN1_RX_AF              GPIO_AF_9
#define CAN1_RX_CLK             RCU_GPIOD
#define CAN1_IRQ                CAN0_RX0_IRQn

/* CAN2 - External Interface */
#define CAN2_PERIPH             CAN1
#define CAN2_CLK                RCU_CAN1
#define CAN2_TX_PORT            GPIOB
#define CAN2_TX_PIN             GPIO_PIN_6
#define CAN2_TX_AF              GPIO_AF_9
#define CAN2_TX_CLK             RCU_GPIOB
#define CAN2_RX_PORT            GPIOB
#define CAN2_RX_PIN             GPIO_PIN_5
#define CAN2_RX_AF              GPIO_AF_9
#define CAN2_RX_CLK             RCU_GPIOB
#define CAN2_IRQ                CAN1_RX0_IRQn

/* Timer Configuration -------------------------------------------------------*/

/* Timer1 - System Tick */
#define TIMER1_PERIPH           TIMER0
#define TIMER1_CLK              RCU_TIMER0
#define TIMER1_IRQ              TIMER0_UP_IRQn

/* Timer2 - Navigation Update */
#define TIMER2_PERIPH           TIMER1
#define TIMER2_CLK              RCU_TIMER1
#define TIMER2_IRQ              TIMER1_IRQn

/* Timer3 - Communication */
#define TIMER3_PERIPH           TIMER2
#define TIMER3_CLK              RCU_TIMER2
#define TIMER3_IRQ              TIMER2_IRQn

/* ADC Configuration ---------------------------------------------------------*/

/* ADC1 - Analog Inputs */
#define ADC1_PERIPH             ADC0
#define ADC1_CLK                RCU_ADC0
#define ADC1_CHANNEL_COUNT      4

#define ADC1_CH0_PORT           GPIOA
#define ADC1_CH0_PIN            GPIO_PIN_0
#define ADC1_CH0_CLK            RCU_GPIOA

#define ADC1_CH1_PORT           GPIOA
#define ADC1_CH1_PIN            GPIO_PIN_1
#define ADC1_CH1_CLK            RCU_GPIOA

#define ADC1_CH2_PORT           GPIOA
#define ADC1_CH2_PIN            GPIO_PIN_2
#define ADC1_CH2_CLK            RCU_GPIOA

#define ADC1_CH3_PORT           GPIOA
#define ADC1_CH3_PIN            GPIO_PIN_3
#define ADC1_CH3_CLK            RCU_GPIOA

/* External Interrupt Configuration ------------------------------------------*/
#define EXTI_LINE_COUNT         16

/* DMA Configuration ---------------------------------------------------------*/
#define DMA_UART1_TX_CHANNEL    DMA_CH6
#define DMA_UART1_RX_CHANNEL    DMA_CH5
#define DMA_SPI1_TX_CHANNEL     DMA_CH3
#define DMA_SPI1_RX_CHANNEL     DMA_CH2
#define DMA_ADC1_CHANNEL        DMA_CH0

/* Power Management ----------------------------------------------------------*/
#define ENABLE_LOW_POWER_MODE   1
#define SLEEP_MODE_TIMEOUT_MS   10000

/* Watchdog Configuration ----------------------------------------------------*/
#define WATCHDOG_PRESCALER      FWDGT_PSC_DIV64
#define WATCHDOG_RELOAD_VALUE   3125    // 5 seconds @ 40kHz/64

#ifdef __cplusplus
}
#endif

#endif /* __HARDWARE_CONFIG_H */

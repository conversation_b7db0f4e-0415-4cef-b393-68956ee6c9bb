/**
  ******************************************************************************
  * @file    systick.h
  * <AUTHOR> Team
  * @brief   Header file for systick module
  * @version V1.0.0
  * @date    2024-12-19
  ******************************************************************************
  */

#ifndef __SYSTICK_H
#define __SYSTICK_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "gd32f4xx.h"

/* Exported types ------------------------------------------------------------*/
/* Exported constants --------------------------------------------------------*/
/* Exported macro ------------------------------------------------------------*/
/* Exported functions --------------------------------------------------------*/

void systick_config(void);
void delay_1ms(uint32_t count);
void delay_decrement(void);

#ifdef __cplusplus
}
#endif

#endif /* __SYSTICK_H */

/**
  ******************************************************************************
  * @file    project_config.h
  * <AUTHOR> Team
  * @brief   Project configuration definitions
  * @version V1.0.0
  * @date    2024-12-19
  ******************************************************************************
  */

#ifndef __PROJECT_CONFIG_H
#define __PROJECT_CONFIG_H

#ifdef __cplusplus
extern "C" {
#endif

/* MCU Configuration --------------------------------------------------------*/
#define MCU_GD32F470VGT6        1
#define MCU_FLASH_SIZE          (3 * 1024 * 1024)  // 3MB Flash
#define MCU_RAM_SIZE            (256 * 1024)       // 256KB RAM
#define MCU_CCRAM_SIZE          (64 * 1024)        // 64KB CCRAM

/* System Clock Configuration -----------------------------------------------*/
#define SYSTEM_CLOCK_FREQ       200000000UL        // 200MHz
#define AHB_CLOCK_FREQ          200000000UL        // 200MHz
#define APB1_CLOCK_FREQ         50000000UL         // 50MHz
#define APB2_CLOCK_FREQ         100000000UL        // 100MHz

/* Hardware Configuration ---------------------------------------------------*/
#define DEVICE_TYPE_INS370      1
#define HARDWARE_VERSION        "v2.0"

/* Feature Enables -----------------------------------------------------------*/
#define ENABLE_NAVIGATION       1
#define ENABLE_COMMUNICATION    1
#define ENABLE_CALIBRATION      1
#define ENABLE_DATA_LOGGING     1
#define ENABLE_FLASH_STORAGE    1
#define ENABLE_CAN_INTERFACE    1
#define ENABLE_ETHERNET         0
#define ENABLE_USB              0
#define ENABLE_RTOS             0

/* Navigation Configuration --------------------------------------------------*/
#define NAV_UPDATE_RATE_HZ      200
#define NAV_IMU_RATE_HZ         1000
#define NAV_GPS_RATE_HZ         10
#define NAV_OUTPUT_RATE_HZ      100

/* Communication Configuration -----------------------------------------------*/
#define COMM_UART_COUNT         4
#define COMM_SPI_COUNT          3
#define COMM_I2C_COUNT          2
#define COMM_CAN_COUNT          2

#define COMM_UART1_BAUDRATE     115200
#define COMM_UART2_BAUDRATE     460800
#define COMM_UART3_BAUDRATE     921600
#define COMM_UART4_BAUDRATE     115200

/* Sensor Configuration ------------------------------------------------------*/
#define SENSOR_IMU_TYPE         SENSOR_ADXL355
#define SENSOR_MAG_TYPE         SENSOR_HMC5983
#define SENSOR_BARO_TYPE        SENSOR_BMP280
#define SENSOR_GPS_TYPE         SENSOR_UBLOX_M8N

/* Memory Configuration ------------------------------------------------------*/
#define FLASH_APP_START_ADDR    0x08010000
#define FLASH_APP_SIZE          (2 * 1024 * 1024)  // 2MB for application
#define FLASH_DATA_START_ADDR   0x08280000
#define FLASH_DATA_SIZE         (512 * 1024)       // 512KB for data storage

#define RAM_HEAP_SIZE           (64 * 1024)        // 64KB heap
#define RAM_STACK_SIZE          (8 * 1024)         // 8KB stack

/* Buffer Sizes --------------------------------------------------------------*/
#define UART_RX_BUFFER_SIZE     1024
#define UART_TX_BUFFER_SIZE     1024
#define SPI_BUFFER_SIZE         256
#define I2C_BUFFER_SIZE         128
#define CAN_RX_BUFFER_SIZE      64
#define CAN_TX_BUFFER_SIZE      64

#define NAV_DATA_BUFFER_SIZE    512
#define LOG_BUFFER_SIZE         2048
#define FLASH_PAGE_BUFFER_SIZE  4096

/* Protocol Configuration ----------------------------------------------------*/
#define PROTOCOL_MAX_PACKET_SIZE    1024
#define PROTOCOL_TIMEOUT_MS         1000
#define PROTOCOL_RETRY_COUNT        3

/* Debug Configuration -------------------------------------------------------*/
#ifdef DEBUG
#define DEBUG_LEVEL             3       // 0=None, 1=Error, 2=Warning, 3=Info, 4=Debug
#define DEBUG_UART              USART0
#define DEBUG_BAUDRATE          115200
#else
#define DEBUG_LEVEL             1
#endif

/* Performance Configuration -------------------------------------------------*/
#define ENABLE_PERFORMANCE_MONITOR     1
#define ENABLE_MEMORY_MONITOR          1
#define ENABLE_TASK_MONITOR            1

/* Safety Configuration ------------------------------------------------------*/
#define ENABLE_WATCHDOG         1
#define WATCHDOG_TIMEOUT_MS     5000
#define ENABLE_BROWNOUT_RESET   1
#define ENABLE_STACK_OVERFLOW_CHECK    1

/* Calibration Configuration -------------------------------------------------*/
#define CALIB_IMU_SAMPLES       1000
#define CALIB_MAG_SAMPLES       500
#define CALIB_TEMP_SAMPLES      100
#define CALIB_TIMEOUT_MS        30000

/* Data Logging Configuration ------------------------------------------------*/
#define LOG_LEVEL_ERROR         1
#define LOG_LEVEL_WARNING       2
#define LOG_LEVEL_INFO          3
#define LOG_LEVEL_DEBUG         4

#define LOG_TO_UART             1
#define LOG_TO_FLASH            1
#define LOG_TO_CAN              0

/* Version Information -------------------------------------------------------*/
#define FIRMWARE_VERSION_MAJOR  1
#define FIRMWARE_VERSION_MINOR  0
#define FIRMWARE_VERSION_PATCH  0
#define FIRMWARE_BUILD_NUMBER   1

#define FIRMWARE_VERSION_STRING "INS370_GD32F470_v1.0.0"

/* Compatibility Definitions ------------------------------------------------*/
#define PORTED_FROM_HPM6750     1
#define MAINTAIN_HPM6750_API    1

#ifdef __cplusplus
}
#endif

#endif /* __PROJECT_CONFIG_H */

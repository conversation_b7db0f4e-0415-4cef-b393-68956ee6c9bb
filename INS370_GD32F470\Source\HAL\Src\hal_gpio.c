/**
  ******************************************************************************
  * @file    hal_gpio.c
  * <AUTHOR> Team
  * @brief   GPIO HAL module implementation
  * @version V1.0.0
  * @date    2024-12-19
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "hal_gpio.h"

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/
/* Private functions ---------------------------------------------------------*/

/**
  * @brief  Initialize GPIO HAL module
  * @retval None
  */
void HAL_GPIO_Init(void)
{
    /* Enable GPIO clocks */
    rcu_periph_clock_enable(RCU_GPIOA);
    rcu_periph_clock_enable(RCU_GPIOB);
    rcu_periph_clock_enable(RCU_GPIOC);
    rcu_periph_clock_enable(RCU_GPIOD);
    rcu_periph_clock_enable(RCU_GPIOE);
    rcu_periph_clock_enable(RCU_GPIOF);
    rcu_periph_clock_enable(RCU_GPIOG);
    rcu_periph_clock_enable(RCU_GPIOH);
    rcu_periph_clock_enable(RCU_GPIOI);
}

/**
  * @brief  De-initialize GPIO HAL module
  * @retval None
  */
void HAL_GPIO_DeInit(void)
{
    /* Reset GPIO peripherals */
    rcu_periph_reset_enable(RCU_GPIOARST);
    rcu_periph_reset_disable(RCU_GPIOARST);
    
    rcu_periph_reset_enable(RCU_GPIOBRST);
    rcu_periph_reset_disable(RCU_GPIOBRST);
    
    rcu_periph_reset_enable(RCU_GPIOCRST);
    rcu_periph_reset_disable(RCU_GPIOCRST);
}

/**
  * @brief  Initialize GPIO pin
  * @param  port: GPIO port
  * @param  gpio_init: GPIO initialization structure
  * @retval None
  */
void HAL_GPIO_Pin_Init(uint32_t port, hal_gpio_init_t* gpio_init)
{
    if (gpio_init == NULL) return;
    
    /* Configure GPIO pin */
    gpio_mode_set(port, gpio_init->mode, gpio_init->pull, gpio_init->pin);
    gpio_output_options_set(port, gpio_init->output_type, gpio_init->speed, gpio_init->pin);
    
    if (gpio_init->mode == GPIO_MODE_AF) {
        gpio_af_set(port, gpio_init->alternate, gpio_init->pin);
    }
}

/**
  * @brief  Write GPIO pin
  * @param  port: GPIO port
  * @param  pin: GPIO pin
  * @param  pin_state: Pin state (SET or RESET)
  * @retval None
  */
void HAL_GPIO_WritePin(uint32_t port, uint32_t pin, uint32_t pin_state)
{
    if (pin_state != HAL_GPIO_PIN_RESET) {
        gpio_bit_set(port, pin);
    } else {
        gpio_bit_reset(port, pin);
    }
}

/**
  * @brief  Read GPIO pin
  * @param  port: GPIO port
  * @param  pin: GPIO pin
  * @retval Pin state
  */
uint32_t HAL_GPIO_ReadPin(uint32_t port, uint32_t pin)
{
    return (gpio_input_bit_get(port, pin) != RESET) ? HAL_GPIO_PIN_SET : HAL_GPIO_PIN_RESET;
}

/**
  * @brief  Toggle GPIO pin
  * @param  port: GPIO port
  * @param  pin: GPIO pin
  * @retval None
  */
void HAL_GPIO_TogglePin(uint32_t port, uint32_t pin)
{
    gpio_bit_toggle(port, pin);
}

/**
  * @brief  Configure GPIO pin mode and pull
  * @param  port: GPIO port
  * @param  pin: GPIO pin
  * @param  mode: GPIO mode
  * @param  pull: GPIO pull configuration
  * @retval None
  */
void HAL_GPIO_ConfigPin(uint32_t port, uint32_t pin, hal_gpio_mode_t mode, hal_gpio_pull_t pull)
{
    gpio_mode_set(port, mode, pull, pin);
}

/**
  * @brief  Configure GPIO alternate function
  * @param  port: GPIO port
  * @param  pin: GPIO pin
  * @param  alternate: Alternate function
  * @retval None
  */
void HAL_GPIO_ConfigAlternate(uint32_t port, uint32_t pin, uint32_t alternate)
{
    gpio_af_set(port, alternate, pin);
}

/**
  * @brief  Enable GPIO clock
  * @param  port: GPIO port
  * @retval None
  */
void HAL_GPIO_EnableClock(uint32_t port)
{
    switch ((uint32_t)port) {
        case (uint32_t)GPIOA:
            rcu_periph_clock_enable(RCU_GPIOA);
            break;
        case (uint32_t)GPIOB:
            rcu_periph_clock_enable(RCU_GPIOB);
            break;
        case (uint32_t)GPIOC:
            rcu_periph_clock_enable(RCU_GPIOC);
            break;
        case (uint32_t)GPIOD:
            rcu_periph_clock_enable(RCU_GPIOD);
            break;
        case (uint32_t)GPIOE:
            rcu_periph_clock_enable(RCU_GPIOE);
            break;
        case (uint32_t)GPIOF:
            rcu_periph_clock_enable(RCU_GPIOF);
            break;
        case (uint32_t)GPIOG:
            rcu_periph_clock_enable(RCU_GPIOG);
            break;
        case (uint32_t)GPIOH:
            rcu_periph_clock_enable(RCU_GPIOH);
            break;
        case (uint32_t)GPIOI:
            rcu_periph_clock_enable(RCU_GPIOI);
            break;
        default:
            break;
    }
}

/**
  * @brief  Disable GPIO clock
  * @param  port: GPIO port
  * @retval None
  */
void HAL_GPIO_DisableClock(uint32_t port)
{
    switch ((uint32_t)port) {
        case (uint32_t)GPIOA:
            rcu_periph_clock_disable(RCU_GPIOA);
            break;
        case (uint32_t)GPIOB:
            rcu_periph_clock_disable(RCU_GPIOB);
            break;
        case (uint32_t)GPIOC:
            rcu_periph_clock_disable(RCU_GPIOC);
            break;
        case (uint32_t)GPIOD:
            rcu_periph_clock_disable(RCU_GPIOD);
            break;
        case (uint32_t)GPIOE:
            rcu_periph_clock_disable(RCU_GPIOE);
            break;
        case (uint32_t)GPIOF:
            rcu_periph_clock_disable(RCU_GPIOF);
            break;
        case (uint32_t)GPIOG:
            rcu_periph_clock_disable(RCU_GPIOG);
            break;
        case (uint32_t)GPIOH:
            rcu_periph_clock_disable(RCU_GPIOH);
            break;
        case (uint32_t)GPIOI:
            rcu_periph_clock_disable(RCU_GPIOI);
            break;
        default:
            break;
    }
}

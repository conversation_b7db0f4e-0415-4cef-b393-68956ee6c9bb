@echo off
REM ============================================================================
REM Quick Start Script for INS370_GD32F470 Project
REM ============================================================================

echo ========================================
echo INS370_GD32F470 Quick Start
echo ========================================
echo.

REM Check if we're in the right directory
if not exist "Project\INS370_GD32F470.uvprojx" (
    echo ERROR: Project file not found!
    echo Please run this script from the INS370_GD32F470 root directory.
    pause
    exit /b 1
)

echo Welcome to INS370_GD32F470 Project!
echo.
echo This project is a modular INS system based on GD32F470 MCU,
echo ported from HPM6750 platform with enhanced architecture.
echo.

:MENU
echo ========================================
echo Please select an option:
echo ========================================
echo 1. Open Keil5 Project
echo 2. Build Project
echo 3. Clean Build
echo 4. View Project Structure
echo 5. Open Documentation
echo 6. Run Tests
echo 7. Exit
echo.
set /p choice="Enter your choice (1-7): "

if "%choice%"=="1" goto OPEN_KEIL
if "%choice%"=="2" goto BUILD
if "%choice%"=="3" goto CLEAN_BUILD
if "%choice%"=="4" goto VIEW_STRUCTURE
if "%choice%"=="5" goto OPEN_DOCS
if "%choice%"=="6" goto RUN_TESTS
if "%choice%"=="7" goto EXIT
echo Invalid choice. Please try again.
goto MENU

:OPEN_KEIL
echo Opening Keil5 project...
if exist "C:\Keil_v5\UV4\UV4.exe" (
    start "" "C:\Keil_v5\UV4\UV4.exe" "Project\INS370_GD32F470.uvprojx"
    echo Keil5 project opened successfully!
) else (
    echo ERROR: Keil5 not found at C:\Keil_v5\UV4\UV4.exe
    echo Please install Keil MDK or update the path.
)
pause
goto MENU

:BUILD
echo Building project...
call Tools\Scripts\build.bat auto
pause
goto MENU

:CLEAN_BUILD
echo Performing clean build...
call Tools\Scripts\build.bat clean
pause
goto MENU

:VIEW_STRUCTURE
echo ========================================
echo Project Structure:
echo ========================================
tree /F /A
pause
goto MENU

:OPEN_DOCS
echo Opening documentation...
if exist "README.md" (
    start "" "README.md"
) else (
    echo Documentation not found.
)
pause
goto MENU

:RUN_TESTS
echo ========================================
echo Available Tests:
echo ========================================
echo 1. Unit Tests
echo 2. Integration Tests  
echo 3. Hardware Tests
echo 4. Back to Main Menu
echo.
set /p test_choice="Enter test choice (1-4): "

if "%test_choice%"=="1" (
    echo Running unit tests...
    if exist "Tests\Unit\run_tests.bat" (
        call Tests\Unit\run_tests.bat
    ) else (
        echo Unit tests not available yet.
    )
)
if "%test_choice%"=="2" (
    echo Running integration tests...
    if exist "Tests\Integration\run_tests.bat" (
        call Tests\Integration\run_tests.bat
    ) else (
        echo Integration tests not available yet.
    )
)
if "%test_choice%"=="3" (
    echo Running hardware tests...
    if exist "Tests\Hardware\run_tests.bat" (
        call Tests\Hardware\run_tests.bat
    ) else (
        echo Hardware tests not available yet.
    )
)
if "%test_choice%"=="4" goto MENU

pause
goto MENU

:EXIT
echo.
echo Thank you for using INS370_GD32F470 Project!
echo.
echo Quick Tips:
echo - Use Keil5 for development and debugging
echo - Check README.md for detailed documentation
echo - Run build.bat for command-line compilation
echo - Refer to Source/Core/Inc/ for configuration
echo.
echo Happy coding!
pause
exit /b 0

/**
  ******************************************************************************
  * @file    system_gd32f4xx.h
  * <AUTHOR> Firmware Team
  * @brief   CMSIS Cortex-M4 Device System Source File for GD32F4xx Device Series
  * @version V1.0.0
  * @date    2024-12-19
  ******************************************************************************
  */

#ifndef SYSTEM_GD32F4XX_H
#define SYSTEM_GD32F4XX_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>

/* Exported constants --------------------------------------------------------*/
extern uint32_t SystemCoreClock;          /*!< System Clock Frequency (Core Clock)  */

/* Exported functions --------------------------------------------------------*/
extern void SystemInit(void);
extern void SystemCoreClockUpdate(void);

/* AHB prescaler table */
static const uint8_t AHB_PSC_TABLE[16] = {0, 0, 0, 0, 0, 0, 0, 0, 1, 2, 3, 4, 6, 7, 8, 9};

/* RCU register definitions (simplified) */
#define RCU_CTL                 (*(volatile uint32_t*)0x40023800)
#define RCU_CFG0                (*(volatile uint32_t*)0x40023808)
#define RCU_CFG1                (*(volatile uint32_t*)0x4002380C)
#define RCU_PLL                 (*(volatile uint32_t*)0x40023804)
#define RCU_INT                 (*(volatile uint32_t*)0x4002380C)

/* RCU_CTL bits */
#define RCU_CTL_HSEEN           (1 << 16)
#define RCU_CTL_HSESTB          (1 << 17)
#define RCU_CTL_PLLEN           (1 << 24)
#define RCU_CTL_PLLSTB          (1 << 25)

/* RCU_CFG0 bits */
#define RCU_CFG0_SCS            (3 << 0)
#define RCU_CFG0_SCSS           (3 << 2)
#define RCU_CFG0_AHBPSC         (15 << 4)

/* RCU_PLL bits */
#define RCU_PLL_PLLMF           (63 << 6)
#define RCU_PLL_PLLMF_4         (1 << 27)
#define RCU_PLL_PLLSEL          (1 << 22)

/* RCU_CFG1 bits */
#define RCU_CFG1_PREDV0         (15 << 0)

/* PLL source definitions */
#define RCU_PLLSRC_HSI_IRC16M_DIV2  0
#define RCU_PLLSRC_HSE              (1 << 22)

/* System clock source definitions */
#define RCU_CKSYSSRC_PLL        (2 << 0)
#define RCU_SCSS_PLL            (2 << 2)

/* AHB prescaler definitions */
#define RCU_AHB_CKSYS_DIV1      (0 << 4)

/* APB prescaler definitions */
#define RCU_APB1_CKAHB_DIV4     (5 << 10)
#define RCU_APB2_CKAHB_DIV2     (4 << 13)

/* FMC register definitions */
#define FMC_WS                  (*(volatile uint32_t*)0x40023C00)
#define FMC_WS_WSCNT            (15 << 0)
#define FMC_WAIT_STATE_5        (5 << 0)

#ifdef __cplusplus
}
#endif

#endif /* SYSTEM_GD32F4XX_H */

/**
  ******************************************************************************
  * @file    ported_setparabao.h
  * <AUTHOR> Team
  * @brief   Header file for SetParaBao ported from HPM6750
  * @version V1.0.0
  * @date    2024-12-19
  ******************************************************************************
  * @attention
  *
  * This module is ported from HPM6750 project and adapted for GD32F470.
  * Original functionality is preserved while adapting to new hardware.
  *
  ******************************************************************************
  */

#ifndef __PORTED_SETPARABAO_H
#define __PORTED_SETPARABAO_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "gd32f4xx.h"
#include "project_config.h"
#include <stdint.h>
#include <stdbool.h>
#include <string.h>

/* Exported types ------------------------------------------------------------*/

/* Parameter structure for navigation system */
typedef struct {
    /* IMU Parameters */
    float gyro_bias[3];         // Gyroscope bias (rad/s)
    float accel_bias[3];        // Accelerometer bias (m/s²)
    float gyro_scale[3];        // Gyroscope scale factor
    float accel_scale[3];       // Accelerometer scale factor
    float gyro_misalign[9];     // Gyroscope misalignment matrix
    float accel_misalign[9];    // Accelerometer misalignment matrix
    
    /* Navigation Parameters */
    double init_latitude;       // Initial latitude (degrees)
    double init_longitude;      // Initial longitude (degrees)
    double init_altitude;       // Initial altitude (meters)
    float init_heading;         // Initial heading (degrees)
    float init_pitch;           // Initial pitch (degrees)
    float init_roll;            // Initial roll (degrees)
    
    /* Filter Parameters */
    float process_noise_gyro;   // Process noise for gyroscope
    float process_noise_accel;  // Process noise for accelerometer
    float measurement_noise_gps; // Measurement noise for GPS
    float measurement_noise_vel; // Measurement noise for velocity
    
    /* Communication Parameters */
    uint32_t output_rate;       // Output data rate (Hz)
    uint32_t baud_rate;         // Communication baud rate
    uint8_t output_format;      // Output data format
    uint8_t protocol_type;      // Communication protocol type
    
    /* System Parameters */
    uint32_t system_id;         // System identification
    uint32_t hardware_version;  // Hardware version
    uint32_t software_version;  // Software version
    uint32_t serial_number;     // Device serial number
    
    /* Calibration Parameters */
    bool auto_calibration;      // Enable auto calibration
    uint32_t calib_duration;    // Calibration duration (seconds)
    float calib_threshold;      // Calibration threshold
    
    /* Advanced Parameters */
    float lever_arm[3];         // Lever arm from IMU to GPS antenna (m)
    float mounting_angles[3];   // Mounting angles (degrees)
    uint8_t coordinate_frame;   // Coordinate frame definition
    
} setparabao_config_t;

/* Parameter validation result */
typedef enum {
    PARAM_VALID = 0,
    PARAM_INVALID_RANGE,
    PARAM_INVALID_FORMAT,
    PARAM_INVALID_CHECKSUM,
    PARAM_FLASH_ERROR,
    PARAM_UNKNOWN_ERROR
} param_result_t;

/* Parameter categories */
typedef enum {
    PARAM_CATEGORY_IMU = 0,
    PARAM_CATEGORY_NAVIGATION,
    PARAM_CATEGORY_FILTER,
    PARAM_CATEGORY_COMMUNICATION,
    PARAM_CATEGORY_SYSTEM,
    PARAM_CATEGORY_CALIBRATION,
    PARAM_CATEGORY_ADVANCED,
    PARAM_CATEGORY_ALL
} param_category_t;

/* Exported constants --------------------------------------------------------*/
#define SETPARABAO_VERSION          "1.0.0"
#define SETPARABAO_MAGIC_NUMBER     0x12345678
#define SETPARABAO_MAX_PARAMS       256
#define SETPARABAO_FLASH_SECTOR     15      // Flash sector for parameter storage

/* Default parameter values */
#define DEFAULT_OUTPUT_RATE         100     // Hz
#define DEFAULT_BAUD_RATE           115200
#define DEFAULT_CALIB_DURATION      60      // seconds
#define DEFAULT_CALIB_THRESHOLD     0.01f

/* Parameter limits */
#define MIN_OUTPUT_RATE             1
#define MAX_OUTPUT_RATE             1000
#define MIN_BAUD_RATE               9600
#define MAX_BAUD_RATE               921600

/* Exported macro ------------------------------------------------------------*/
#define SETPARABAO_PARAM_ADDR(offset)   (FLASH_DATA_START_ADDR + (offset))

/* Exported functions --------------------------------------------------------*/

/* Initialization and configuration */
void Ported_SetParaBao_Init(void);
void Ported_SetParaBao_DeInit(void);
param_result_t Ported_SetParaBao_LoadDefaults(void);
param_result_t Ported_SetParaBao_Reset(void);

/* Parameter access functions */
param_result_t Ported_SetParaBao_GetConfig(setparabao_config_t* config);
param_result_t Ported_SetParaBao_SetConfig(const setparabao_config_t* config);
param_result_t Ported_SetParaBao_GetParameter(uint16_t param_id, void* value, uint16_t size);
param_result_t Ported_SetParaBao_SetParameter(uint16_t param_id, const void* value, uint16_t size);

/* Parameter validation */
param_result_t Ported_SetParaBao_ValidateConfig(const setparabao_config_t* config);
param_result_t Ported_SetParaBao_ValidateParameter(uint16_t param_id, const void* value, uint16_t size);

/* Flash operations */
param_result_t Ported_SetParaBao_SaveToFlash(void);
param_result_t Ported_SetParaBao_LoadFromFlash(void);
param_result_t Ported_SetParaBao_EraseFlash(void);

/* Backup and restore */
param_result_t Ported_SetParaBao_Backup(uint8_t* buffer, uint32_t buffer_size);
param_result_t Ported_SetParaBao_Restore(const uint8_t* buffer, uint32_t buffer_size);

/* Utility functions */
uint32_t Ported_SetParaBao_CalculateChecksum(const setparabao_config_t* config);
bool Ported_SetParaBao_IsConfigValid(void);
void Ported_SetParaBao_PrintConfig(void);

/* Category-specific functions */
param_result_t Ported_SetParaBao_GetIMUParams(float* gyro_bias, float* accel_bias);
param_result_t Ported_SetParaBao_SetIMUParams(const float* gyro_bias, const float* accel_bias);
param_result_t Ported_SetParaBao_GetNavParams(double* lat, double* lon, double* alt);
param_result_t Ported_SetParaBao_SetNavParams(double lat, double lon, double alt);

/* Communication interface functions (compatible with HPM6750) */
void SetParaBao_ProcessCommand(const uint8_t* cmd_buffer, uint16_t cmd_length);
void SetParaBao_SendResponse(uint8_t* response_buffer, uint16_t* response_length);
void SetParaBao_HandleUARTData(const uint8_t* data, uint16_t length);

/* Legacy compatibility functions */
#define SetParaBao_Init()                   Ported_SetParaBao_Init()
#define SetParaBao_GetConfig(cfg)           Ported_SetParaBao_GetConfig(cfg)
#define SetParaBao_SetConfig(cfg)           Ported_SetParaBao_SetConfig(cfg)
#define SetParaBao_SaveToFlash()            Ported_SetParaBao_SaveToFlash()
#define SetParaBao_LoadFromFlash()          Ported_SetParaBao_LoadFromFlash()

#ifdef __cplusplus
}
#endif

#endif /* __PORTED_SETPARABAO_H */

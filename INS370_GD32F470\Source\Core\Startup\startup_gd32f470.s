;******************** (C) COPYRIGHT 2016 GigaDevice ********************
;* File Name          : startup_gd32f470.s
;* Author             : GigaDevice Firmware Team
;* Version            : V1.0.0
;* Date               : 2016-10-19
;* Description        : GD32F470 vector table for MDK-ARM toolchain.
;*                      This module performs:
;*                      - Set the initial SP
;*                      - Set the initial PC == Reset_Handler
;*                      - Set the vector table entries with the exceptions ISR address
;*                      - Configure the clock system
;*                      - Branches to __main in the C library (which eventually
;*                        calls main()).
;*                      After Reset the CortexM4 processor is in Thread mode,
;*                      priority is Privileged, and the Stack is set to Main.
;* <<< Use Configuration Wizard in Context Menu >>>
;***************************************************************************

; Amount of memory (in bytes) allocated for Stack
; Tailor this value to your application needs
; <h> Stack Configuration
;   <o> Stack Size (in Bytes) <0x0-0xFFFFFFFF:8>
; </h>

Stack_Size      EQU     0x00000400

                AREA    STACK, NOINIT, READWRITE, ALIGN=3
Stack_Mem       SPACE   Stack_Size
__initial_sp

; <h> Heap Configuration
;   <o>  Heap Size (in Bytes) <0x0-0xFFFFFFFF:8>
; </h>

Heap_Size       EQU     0x00000200

                AREA    HEAP, NOINIT, READWRITE, ALIGN=3
__heap_base
Heap_Mem        SPACE   Heap_Size
__heap_limit

                PRESERVE8
                THUMB

; Vector Table Mapped to Address 0 at Reset
                AREA    RESET, DATA, READONLY
                EXPORT  __Vectors
                EXPORT  __Vectors_End
                EXPORT  __Vectors_Size

__Vectors       DCD     __initial_sp               ; Top of Stack
                DCD     Reset_Handler              ; Reset Handler
                DCD     NMI_Handler                ; NMI Handler
                DCD     HardFault_Handler          ; Hard Fault Handler
                DCD     MemManage_Handler          ; MPU Fault Handler
                DCD     BusFault_Handler           ; Bus Fault Handler
                DCD     UsageFault_Handler         ; Usage Fault Handler
                DCD     0                          ; Reserved
                DCD     0                          ; Reserved
                DCD     0                          ; Reserved
                DCD     0                          ; Reserved
                DCD     SVC_Handler                ; SVCall Handler
                DCD     DebugMon_Handler           ; Debug Monitor Handler
                DCD     0                          ; Reserved
                DCD     PendSV_Handler             ; PendSV Handler
                DCD     SysTick_Handler            ; SysTick Handler

                ; External Interrupts
                DCD     WWDGT_IRQHandler           ; 16:Window Watchdog Timer
                DCD     LVD_IRQHandler             ; 17:LVD through EXTI Line detect
                DCD     TAMPER_IRQHandler          ; 18:Tamper through EXTI Line detect
                DCD     RTC_IRQHandler             ; 19:RTC through EXTI Line
                DCD     FMC_IRQHandler             ; 20:FMC
                DCD     RCU_CTC_IRQHandler         ; 21:RCU and CTC
                DCD     EXTI0_IRQHandler           ; 22:EXTI Line 0
                DCD     EXTI1_IRQHandler           ; 23:EXTI Line 1
                DCD     EXTI2_IRQHandler           ; 24:EXTI Line 2
                DCD     EXTI3_IRQHandler           ; 25:EXTI Line 3
                DCD     EXTI4_IRQHandler           ; 26:EXTI Line 4
                DCD     DMA0_Channel0_IRQHandler   ; 27:DMA0 Channel0
                DCD     DMA0_Channel1_IRQHandler   ; 28:DMA0 Channel1
                DCD     DMA0_Channel2_IRQHandler   ; 29:DMA0 Channel2
                DCD     DMA0_Channel3_IRQHandler   ; 30:DMA0 Channel3
                DCD     DMA0_Channel4_IRQHandler   ; 31:DMA0 Channel4
                DCD     DMA0_Channel5_IRQHandler   ; 32:DMA0 Channel5
                DCD     DMA0_Channel6_IRQHandler   ; 33:DMA0 Channel6
                DCD     ADC_IRQHandler             ; 34:ADC
                DCD     CAN0_TX_IRQHandler         ; 35:CAN0 TX
                DCD     CAN0_RX0_IRQHandler        ; 36:CAN0 RX0
                DCD     CAN0_RX1_IRQHandler        ; 37:CAN0 RX1
                DCD     CAN0_EWMC_IRQHandler       ; 38:CAN0 EWMC
                DCD     EXTI5_9_IRQHandler         ; 39:EXTI5 to EXTI9
                DCD     TIMER0_BRK_TIMER8_IRQHandler ; 40:TIMER0 Break and TIMER8
                DCD     TIMER0_UP_TIMER9_IRQHandler  ; 41:TIMER0 Update and TIMER9
                DCD     TIMER0_TRG_CMT_TIMER10_IRQHandler ; 42:TIMER0 Trigger and Commutation and TIMER10
                DCD     TIMER0_Channel_IRQHandler  ; 43:TIMER0 Channel Capture Compare
                DCD     TIMER1_IRQHandler          ; 44:TIMER1
                DCD     TIMER2_IRQHandler          ; 45:TIMER2
                DCD     TIMER3_IRQHandler          ; 46:TIMER3
                DCD     I2C0_EV_IRQHandler         ; 47:I2C0 Event
                DCD     I2C0_ER_IRQHandler         ; 48:I2C0 Error
                DCD     I2C1_EV_IRQHandler         ; 49:I2C1 Event
                DCD     I2C1_ER_IRQHandler         ; 50:I2C1 Error
                DCD     SPI0_IRQHandler            ; 51:SPI0
                DCD     SPI1_IRQHandler            ; 52:SPI1
                DCD     USART0_IRQHandler          ; 53:USART0
                DCD     USART1_IRQHandler          ; 54:USART1
                DCD     USART2_IRQHandler          ; 55:USART2
                DCD     EXTI10_15_IRQHandler       ; 56:EXTI10 to EXTI15
                DCD     RTC_Alarm_IRQHandler       ; 57:RTC Alarm
                DCD     USBFS_WKUP_IRQHandler      ; 58:USBFS Wakeup
                DCD     TIMER7_BRK_TIMER11_IRQHandler ; 59:TIMER7 Break and TIMER11
                DCD     TIMER7_UP_TIMER12_IRQHandler  ; 60:TIMER7 Update and TIMER12
                DCD     TIMER7_TRG_CMT_TIMER13_IRQHandler ; 61:TIMER7 Trigger and Commutation and TIMER13
                DCD     TIMER7_Channel_IRQHandler  ; 62:TIMER7 Channel Capture Compare
                DCD     DMA0_Channel7_IRQHandler   ; 63:DMA0 Channel7

__Vectors_End

__Vectors_Size  EQU  __Vectors_End - __Vectors

                AREA    |.text|, CODE, READONLY

; Reset handler
Reset_Handler    PROC
                 EXPORT  Reset_Handler             [WEAK]
                 IMPORT  SystemInit
                 IMPORT  __main

                 LDR     R0, =SystemInit
                 BLX     R0
                 LDR     R0, =__main
                 BX      R0
                 ENDP

; Dummy Exception Handlers (infinite loops which can be modified)

NMI_Handler     PROC
                EXPORT  NMI_Handler                [WEAK]
                B       .
                ENDP
HardFault_Handler\
                PROC
                EXPORT  HardFault_Handler          [WEAK]
                B       .
                ENDP
MemManage_Handler\
                PROC
                EXPORT  MemManage_Handler          [WEAK]
                B       .
                ENDP
BusFault_Handler\
                PROC
                EXPORT  BusFault_Handler           [WEAK]
                B       .
                ENDP
UsageFault_Handler\
                PROC
                EXPORT  UsageFault_Handler         [WEAK]
                B       .
                ENDP
SVC_Handler     PROC
                EXPORT  SVC_Handler                [WEAK]
                B       .
                ENDP
DebugMon_Handler\
                PROC
                EXPORT  DebugMon_Handler           [WEAK]
                B       .
                ENDP
PendSV_Handler  PROC
                EXPORT  PendSV_Handler             [WEAK]
                B       .
                ENDP
SysTick_Handler PROC
                EXPORT  SysTick_Handler            [WEAK]
                B       .
                ENDP

Default_Handler PROC

                EXPORT  WWDGT_IRQHandler           [WEAK]
                EXPORT  LVD_IRQHandler             [WEAK]
                EXPORT  TAMPER_IRQHandler          [WEAK]
                EXPORT  RTC_IRQHandler             [WEAK]
                EXPORT  FMC_IRQHandler             [WEAK]
                EXPORT  RCU_CTC_IRQHandler         [WEAK]
                EXPORT  EXTI0_IRQHandler           [WEAK]
                EXPORT  EXTI1_IRQHandler           [WEAK]
                EXPORT  EXTI2_IRQHandler           [WEAK]
                EXPORT  EXTI3_IRQHandler           [WEAK]
                EXPORT  EXTI4_IRQHandler           [WEAK]
                EXPORT  DMA0_Channel0_IRQHandler   [WEAK]
                EXPORT  DMA0_Channel1_IRQHandler   [WEAK]
                EXPORT  DMA0_Channel2_IRQHandler   [WEAK]
                EXPORT  DMA0_Channel3_IRQHandler   [WEAK]
                EXPORT  DMA0_Channel4_IRQHandler   [WEAK]
                EXPORT  DMA0_Channel5_IRQHandler   [WEAK]
                EXPORT  DMA0_Channel6_IRQHandler   [WEAK]
                EXPORT  ADC_IRQHandler             [WEAK]
                EXPORT  CAN0_TX_IRQHandler         [WEAK]
                EXPORT  CAN0_RX0_IRQHandler        [WEAK]
                EXPORT  CAN0_RX1_IRQHandler        [WEAK]
                EXPORT  CAN0_EWMC_IRQHandler       [WEAK]
                EXPORT  EXTI5_9_IRQHandler         [WEAK]
                EXPORT  TIMER0_BRK_TIMER8_IRQHandler [WEAK]
                EXPORT  TIMER0_UP_TIMER9_IRQHandler [WEAK]
                EXPORT  TIMER0_TRG_CMT_TIMER10_IRQHandler [WEAK]
                EXPORT  TIMER0_Channel_IRQHandler  [WEAK]
                EXPORT  TIMER1_IRQHandler          [WEAK]
                EXPORT  TIMER2_IRQHandler          [WEAK]
                EXPORT  TIMER3_IRQHandler          [WEAK]
                EXPORT  I2C0_EV_IRQHandler         [WEAK]
                EXPORT  I2C0_ER_IRQHandler         [WEAK]
                EXPORT  I2C1_EV_IRQHandler         [WEAK]
                EXPORT  I2C1_ER_IRQHandler         [WEAK]
                EXPORT  SPI0_IRQHandler            [WEAK]
                EXPORT  SPI1_IRQHandler            [WEAK]
                EXPORT  USART0_IRQHandler          [WEAK]
                EXPORT  USART1_IRQHandler          [WEAK]
                EXPORT  USART2_IRQHandler          [WEAK]
                EXPORT  EXTI10_15_IRQHandler       [WEAK]
                EXPORT  RTC_Alarm_IRQHandler       [WEAK]
                EXPORT  USBFS_WKUP_IRQHandler      [WEAK]
                EXPORT  TIMER7_BRK_TIMER11_IRQHandler [WEAK]
                EXPORT  TIMER7_UP_TIMER12_IRQHandler [WEAK]
                EXPORT  TIMER7_TRG_CMT_TIMER13_IRQHandler [WEAK]
                EXPORT  TIMER7_Channel_IRQHandler  [WEAK]
                EXPORT  DMA0_Channel7_IRQHandler   [WEAK]

WWDGT_IRQHandler
LVD_IRQHandler
TAMPER_IRQHandler
RTC_IRQHandler
FMC_IRQHandler
RCU_CTC_IRQHandler
EXTI0_IRQHandler
EXTI1_IRQHandler
EXTI2_IRQHandler
EXTI3_IRQHandler
EXTI4_IRQHandler
DMA0_Channel0_IRQHandler
DMA0_Channel1_IRQHandler
DMA0_Channel2_IRQHandler
DMA0_Channel3_IRQHandler
DMA0_Channel4_IRQHandler
DMA0_Channel5_IRQHandler
DMA0_Channel6_IRQHandler
ADC_IRQHandler
CAN0_TX_IRQHandler
CAN0_RX0_IRQHandler
CAN0_RX1_IRQHandler
CAN0_EWMC_IRQHandler
EXTI5_9_IRQHandler
TIMER0_BRK_TIMER8_IRQHandler
TIMER0_UP_TIMER9_IRQHandler
TIMER0_TRG_CMT_TIMER10_IRQHandler
TIMER0_Channel_IRQHandler
TIMER1_IRQHandler
TIMER2_IRQHandler
TIMER3_IRQHandler
I2C0_EV_IRQHandler
I2C0_ER_IRQHandler
I2C1_EV_IRQHandler
I2C1_ER_IRQHandler
SPI0_IRQHandler
SPI1_IRQHandler
USART0_IRQHandler
USART1_IRQHandler
USART2_IRQHandler
EXTI10_15_IRQHandler
RTC_Alarm_IRQHandler
USBFS_WKUP_IRQHandler
TIMER7_BRK_TIMER11_IRQHandler
TIMER7_UP_TIMER12_IRQHandler
TIMER7_TRG_CMT_TIMER13_IRQHandler
TIMER7_Channel_IRQHandler
DMA0_Channel7_IRQHandler

                B       .

                ENDP

                ALIGN

;*******************************************************************************
; User Stack and Heap initialization
;*******************************************************************************
                 IF      :DEF:__MICROLIB

                 EXPORT  __initial_sp
                 EXPORT  __heap_base
                 EXPORT  __heap_limit

                 ELSE

                 IMPORT  __use_two_region_memory
                 EXPORT  __user_initial_stackheap

__user_initial_stackheap

                 LDR     R0, =  Heap_Mem
                 LDR     R1, =(Stack_Mem + Stack_Size)
                 LDR     R2, = (Heap_Mem +  Heap_Size)
                 LDR     R3, = Stack_Mem
                 BX      LR

                 ALIGN

                 ENDIF

                 END

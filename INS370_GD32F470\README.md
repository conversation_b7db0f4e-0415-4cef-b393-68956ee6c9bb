# INS370_GD32F470 项目

## 项目概述
基于GD32F470微控制器的惯性导航系统，采用模块化设计架构。本项目从HPM6750平台移植而来，保持了原有功能的同时，针对GD32F470进行了优化。

## 🚀 主要特性
- **高性能MCU**: GD32F470VGT6 @ 200MHz
- **模块化架构**: 清晰的分层设计，易于维护和扩展
- **完整移植**: 保留HPM6750项目的核心功能
- **实时导航**: 200Hz导航更新率，1000Hz IMU数据率
- **多种通信接口**: UART、SPI、I2C、CAN
- **灵活配置**: 支持参数配置和在线标定

## 📁 目录结构

```
INS370_GD32F470/
├── Source/                     # 源代码
│   ├── Core/                   # 核心系统文件
│   ├── HAL/                    # 硬件抽象层
│   ├── BSP/                    # 板级支持包
│   ├── App/                    # 应用层模块
│   ├── Middleware/             # 中间件
│   └── Ported/                 # 移植功能
├── Library/                    # 库文件
├── Project/                    # Keil项目文件
├── Config/                     # 配置文件
├── Output/                     # 编译输出
├── Tools/                      # 开发工具
├── Tests/                      # 测试代码
└── Documentation/              # 文档
```

## 🛠 开发环境

### 硬件要求
- **MCU**: GD32F470VGT6
- **Flash**: 3MB
- **RAM**: 256KB + 64KB CCRAM
- **时钟**: 200MHz主频

### 软件要求
- **IDE**: Keil MDK 5.36+
- **编译器**: ARM Compiler 6
- **调试器**: J-Link / ST-Link
- **设备包**: GigaDevice.GD32F4xx_DFP.3.0.4+

## 🔧 编译说明

### 方法1: 使用Keil5 IDE
1. 打开 `Project/INS370_GD32F470.uvprojx`
2. 选择目标配置 (Debug/Release)
3. 点击编译按钮或按F7

### 方法2: 使用命令行
```bash
cd INS370_GD32F470
Tools\Scripts\build.bat
```

### 清理编译
```bash
Tools\Scripts\build.bat clean
```

## 📋 配置说明

### 系统配置
- 主要配置文件: `Source/Core/Inc/project_config.h`
- 硬件配置文件: `Source/Core/Inc/hardware_config.h`

### 关键配置项
```c
#define SYSTEM_CLOCK_FREQ       200000000UL    // 系统时钟
#define NAV_UPDATE_RATE_HZ      200            // 导航更新率
#define COMM_UART1_BAUDRATE     115200         // 调试串口波特率
#define ENABLE_NAVIGATION       1              // 启用导航功能
#define ENABLE_COMMUNICATION    1              // 启用通信功能
```

## 🚦 运行说明

### 系统启动流程
1. **硬件初始化**: GPIO、时钟、外设初始化
2. **应用初始化**: 导航、通信、传感器模块初始化
3. **系统运行**: 进入主循环，处理导航和通信任务

### LED指示
- **LED1 (绿色)**: 电源指示灯，系统正常时常亮
- **LED2 (蓝色)**: 状态指示灯，系统运行时闪烁
- **LED3 (红色)**: 错误指示灯，系统错误时亮起

### 串口输出
- **UART1**: 调试信息输出 (115200 bps)
- **UART2**: GPS数据接收 (460800 bps)
- **UART3**: 外部通信 (921600 bps)
- **UART4**: 数据输出 (115200 bps)

## 🔄 移植说明

### 从HPM6750移植的功能
1. **SetParaBao**: 参数配置和管理
2. **Communication**: 通信协议适配
3. **Flash**: Flash存储管理
4. **Integration**: 系统集成示例

### 兼容性
- 保持HPM6750 API兼容性
- 提供Legacy函数映射
- 支持原有配置格式

## 🧪 测试说明

### 单元测试
```bash
cd Tests/Unit
# 运行单元测试脚本
```

### 硬件测试
```bash
cd Tests/Hardware
# 运行硬件测试脚本
```

## 📖 API文档

### 核心API
- `System_Init()`: 系统初始化
- `System_Run()`: 系统运行
- `System_GetStatus()`: 获取系统状态

### 导航API
- `App_Navigation_Init()`: 导航初始化
- `App_Navigation_Update()`: 导航更新
- `App_Navigation_GetOutput()`: 获取导航输出

### 通信API
- `App_Communication_Init()`: 通信初始化
- `App_Communication_Send()`: 发送数据
- `App_Communication_Receive()`: 接收数据

## 🐛 调试说明

### 调试配置
- 使用J-Link或ST-Link调试器
- 支持实时调试和变量监控
- 提供RTT输出支持

### 常见问题
1. **编译错误**: 检查设备包是否正确安装
2. **下载失败**: 检查调试器连接和目标板电源
3. **运行异常**: 检查时钟配置和外设初始化

## 📝 版本信息
- **版本**: v1.0.0
- **创建日期**: 2024-12-19
- **基于**: HPM6750项目移植
- **MCU**: GD32F470VGT6
- **架构**: 模块化设计

## 👥 贡献指南
1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 📄 许可证
本项目采用MIT许可证，详见LICENSE文件。

## 📞 联系方式
如有问题或建议，请联系开发团队。

/**
  ******************************************************************************
  * @file    bsp_led.c
  * <AUTHOR> Team
  * @brief   LED BSP module implementation
  * @version V1.0.0
  * @date    2024-12-19
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "bsp_led.h"

/* Private typedef -----------------------------------------------------------*/
typedef struct {
    uint32_t port;
    uint32_t pin;
    uint32_t clock;
    bsp_led_mode_t mode;
    uint32_t last_toggle;
    bool state;
} led_config_t;

/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
static led_config_t led_configs[LED_COUNT] = {
    {LED1_PORT, LED1_PIN, LED1_CLK, LED_MODE_OFF, 0, false},  // LED1
    {LED2_PORT, LED2_PIN, LED2_CLK, LED_MODE_OFF, 0, false},  // LED2
    {LED3_PORT, LED3_PIN, LED3_CLK, LED_MODE_OFF, 0, false},  // LED3
};

/* Private function prototypes -----------------------------------------------*/
static uint32_t BSP_LED_GetTick(void);

/* Private functions ---------------------------------------------------------*/

/**
  * @brief  Get system tick (placeholder implementation)
  * @retval Current tick in milliseconds
  */
static uint32_t BSP_LED_GetTick(void)
{
    // This should be replaced with actual system tick function
    // For now, return a dummy value
    static uint32_t dummy_tick = 0;
    return dummy_tick++;
}

/**
  * @brief  Initialize LED BSP module
  * @retval None
  */
void BSP_LED_Init(void)
{
    hal_gpio_init_t gpio_init;
    
    /* Configure all LEDs */
    for (int i = 0; i < LED_COUNT; i++) {
        /* Enable GPIO clock */
        HAL_GPIO_EnableClock(led_configs[i].port);
        
        /* Configure GPIO pin */
        gpio_init.pin = led_configs[i].pin;
        gpio_init.mode = HAL_GPIO_MODE_OUTPUT;
        gpio_init.pull = HAL_GPIO_PULL_NONE;
        gpio_init.speed = HAL_GPIO_SPEED_LOW;
        gpio_init.output_type = HAL_GPIO_OUTPUT_PP;
        gpio_init.alternate = 0;
        
        HAL_GPIO_Pin_Init(led_configs[i].port, &gpio_init);
        
        /* Turn off LED initially */
        HAL_GPIO_WritePin(led_configs[i].port, led_configs[i].pin, HAL_GPIO_PIN_SET);
        led_configs[i].state = false;
        led_configs[i].mode = LED_MODE_OFF;
    }
}

/**
  * @brief  De-initialize LED BSP module
  * @retval None
  */
void BSP_LED_DeInit(void)
{
    /* Turn off all LEDs */
    BSP_LED_AllOff();
    
    /* Reset GPIO configurations */
    for (int i = 0; i < LED_COUNT; i++) {
        HAL_GPIO_DisableClock(led_configs[i].port);
    }
}

/**
  * @brief  Turn on LED
  * @param  led: LED to turn on
  * @retval None
  */
void BSP_LED_On(bsp_led_t led)
{
    if (led >= LED_COUNT) return;
    
    HAL_GPIO_WritePin(led_configs[led].port, led_configs[led].pin, HAL_GPIO_PIN_RESET);
    led_configs[led].state = true;
    led_configs[led].mode = LED_MODE_ON;
}

/**
  * @brief  Turn off LED
  * @param  led: LED to turn off
  * @retval None
  */
void BSP_LED_Off(bsp_led_t led)
{
    if (led >= LED_COUNT) return;
    
    HAL_GPIO_WritePin(led_configs[led].port, led_configs[led].pin, HAL_GPIO_PIN_SET);
    led_configs[led].state = false;
    led_configs[led].mode = LED_MODE_OFF;
}

/**
  * @brief  Toggle LED
  * @param  led: LED to toggle
  * @retval None
  */
void BSP_LED_Toggle(bsp_led_t led)
{
    if (led >= LED_COUNT) return;
    
    HAL_GPIO_TogglePin(led_configs[led].port, led_configs[led].pin);
    led_configs[led].state = !led_configs[led].state;
}

/**
  * @brief  Set LED mode
  * @param  led: LED to configure
  * @param  mode: LED mode
  * @retval None
  */
void BSP_LED_SetMode(bsp_led_t led, bsp_led_mode_t mode)
{
    if (led >= LED_COUNT) return;
    
    led_configs[led].mode = mode;
    led_configs[led].last_toggle = BSP_LED_GetTick();
    
    switch (mode) {
        case LED_MODE_OFF:
            BSP_LED_Off(led);
            break;
        case LED_MODE_ON:
            BSP_LED_On(led);
            break;
        case LED_MODE_BLINK_SLOW:
        case LED_MODE_BLINK_FAST:
        case LED_MODE_PULSE:
            /* Will be handled in BSP_LED_Update() */
            break;
        default:
            BSP_LED_Off(led);
            break;
    }
}

/**
  * @brief  Update LED states (call this periodically)
  * @retval None
  */
void BSP_LED_Update(void)
{
    uint32_t current_tick = BSP_LED_GetTick();
    
    for (int i = 0; i < LED_COUNT; i++) {
        uint32_t period = 0;
        
        switch (led_configs[i].mode) {
            case LED_MODE_BLINK_SLOW:
                period = LED_BLINK_SLOW_PERIOD;
                break;
            case LED_MODE_BLINK_FAST:
                period = LED_BLINK_FAST_PERIOD;
                break;
            case LED_MODE_PULSE:
                period = LED_PULSE_PERIOD;
                break;
            default:
                continue;
        }
        
        if ((current_tick - led_configs[i].last_toggle) >= (period / 2)) {
            BSP_LED_Toggle((bsp_led_t)i);
            led_configs[i].last_toggle = current_tick;
        }
    }
}

/**
  * @brief  Turn on all LEDs
  * @retval None
  */
void BSP_LED_AllOn(void)
{
    for (int i = 0; i < LED_COUNT; i++) {
        BSP_LED_On((bsp_led_t)i);
    }
}

/**
  * @brief  Turn off all LEDs
  * @retval None
  */
void BSP_LED_AllOff(void)
{
    for (int i = 0; i < LED_COUNT; i++) {
        BSP_LED_Off((bsp_led_t)i);
    }
}

/**
  * @brief  Test all LEDs
  * @retval None
  */
void BSP_LED_Test(void)
{
    /* Turn on all LEDs */
    BSP_LED_AllOn();
    
    /* Wait (this should be replaced with proper delay function) */
    for (volatile uint32_t i = 0; i < 1000000; i++);
    
    /* Turn off all LEDs */
    BSP_LED_AllOff();
    
    /* Wait */
    for (volatile uint32_t i = 0; i < 1000000; i++);
    
    /* Test each LED individually */
    for (int led = 0; led < LED_COUNT; led++) {
        BSP_LED_On((bsp_led_t)led);
        for (volatile uint32_t i = 0; i < 500000; i++);
        BSP_LED_Off((bsp_led_t)led);
        for (volatile uint32_t i = 0; i < 500000; i++);
    }
}

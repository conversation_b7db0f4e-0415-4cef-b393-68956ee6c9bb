/**
  ******************************************************************************
  * @file    main.h
  * <AUTHOR> Team
  * @brief   Header for main.c module
  * @version V1.0.0
  * @date    2024-12-19
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2024 INS370 Project.
  * All rights reserved.
  *
  ******************************************************************************
  */

#ifndef __MAIN_H
#define __MAIN_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "gd32f4xx.h"
#include "systick.h"
#include <stdio.h>
#include <stdint.h>
#include <stdbool.h>
#include <string.h>

/* Project Configuration -----------------------------------------------------*/
#include "project_config.h"
#include "hardware_config.h"

/* HAL Layer -----------------------------------------------------------------*/
#include "hal_gpio.h"
#include "hal_uart.h"
#include "hal_spi.h"
#include "hal_i2c.h"
#include "hal_can.h"
#include "hal_timer.h"
#include "hal_adc.h"
#include "hal_flash.h"

/* BSP Layer -----------------------------------------------------------------*/
#include "bsp_led.h"
#include "bsp_button.h"
#include "bsp_sensor.h"
#include "bsp_communication.h"

/* Application Layer ---------------------------------------------------------*/
#include "app_navigation.h"
#include "app_communication.h"
#include "app_data_process.h"
#include "app_calibration.h"
#include "app_system.h"

/* Middleware ----------------------------------------------------------------*/
#include "middleware_protocol.h"

/* Ported HPM6750 Functions -------------------------------------------------*/
#include "ported_setparabao.h"
// #include "ported_communication.h"
// #include "ported_flash.h"
// #include "ported_integration.h"

/* Empty declarations for initial compilation --------------------------------*/
#include "empty_declarations.h"

/* Exported types ------------------------------------------------------------*/
typedef enum {
    SYSTEM_STATE_INIT = 0,
    SYSTEM_STATE_READY,
    SYSTEM_STATE_RUNNING,
    SYSTEM_STATE_CALIBRATION,
    SYSTEM_STATE_ERROR,
    SYSTEM_STATE_SHUTDOWN
} system_state_t;

typedef struct {
    system_state_t state;
    uint32_t timestamp;
    uint32_t error_code;
    bool navigation_ready;
    bool communication_ready;
    bool sensor_ready;
} system_status_t;

/* Exported constants --------------------------------------------------------*/
#define SYSTEM_VERSION_MAJOR    1
#define SYSTEM_VERSION_MINOR    0
#define SYSTEM_VERSION_PATCH    0

#define SYSTEM_TICK_FREQ        1000    // 1ms system tick
#define NAVIGATION_FREQ         200     // 200Hz navigation update
#define COMMUNICATION_FREQ      100     // 100Hz communication update

/* Exported macro ------------------------------------------------------------*/
#define SYSTEM_VERSION_STRING   "v1.0.0"
#define BUILD_DATE              __DATE__
#define BUILD_TIME              __TIME__

/* Exported functions prototypes ---------------------------------------------*/
void SystemClock_Config(void);
void System_Init(void);
void System_Run(void);
void System_Error_Handler(void);

/* System Status Functions */
system_status_t* System_GetStatus(void);
void System_SetState(system_state_t new_state);
system_state_t System_GetState(void);

/* System Utilities */
uint32_t System_GetTick(void);
void System_Delay(uint32_t delay_ms);
void System_Reset(void);

/* Debug and Logging */
#ifdef DEBUG
#define DEBUG_PRINTF(fmt, ...) printf("[DEBUG] " fmt "\r\n", ##__VA_ARGS__)
#define ERROR_PRINTF(fmt, ...) printf("[ERROR] " fmt "\r\n", ##__VA_ARGS__)
#define INFO_PRINTF(fmt, ...)  printf("[INFO] " fmt "\r\n", ##__VA_ARGS__)
#else
#define DEBUG_PRINTF(fmt, ...)
#define ERROR_PRINTF(fmt, ...)
#define INFO_PRINTF(fmt, ...)
#endif

#ifdef __cplusplus
}
#endif

#endif /* __MAIN_H */

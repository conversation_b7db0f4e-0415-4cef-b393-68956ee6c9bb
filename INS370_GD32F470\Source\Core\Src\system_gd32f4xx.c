/**
  ******************************************************************************
  * @file    system_gd32f4xx.c
  * <AUTHOR> Firmware Team
  * @brief   CMSIS Cortex-M4 Device Peripheral Access Layer System Source File
  * @version V1.0.0
  * @date    2024-12-19
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "gd32f4xx.h"

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
#define VECT_TAB_SRAM
#define VECT_TAB_OFFSET  0x00 /*!< Vector Table base offset field. 
                                   This value must be a multiple of 0x200. */

/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
uint32_t SystemCoreClock = 16000000;

/* Private function prototypes -----------------------------------------------*/
static void SetSysClock(void);

/* Private functions ---------------------------------------------------------*/

/**
  * @brief  Setup the microcontroller system
  *         Initialize the FPU setting, vector table location and External memory 
  *         configuration.
  * @param  None
  * @retval None
  */
void SystemInit(void)
{
    /* FPU settings ------------------------------------------------------------*/
#if (__FPU_PRESENT == 1) && (__FPU_USED == 1)
    SCB->CPACR |= ((3UL << 10*2)|(3UL << 11*2));  /* set CP10 and CP11 Full Access */
#endif

    /* Reset the RCU clock configuration to the default reset state ------------*/
    /* Set HSIEN bit */
    RCU_CTL |= (uint32_t)0x00000001;

    /* Reset CFGR register */
    RCU_CFG0 = 0x00000000;

    /* Reset HSEEN, CSSON and PLLEN bits */
    RCU_CTL &= (uint32_t)0xFEF6FFFF;

    /* Reset PLLCFGR register */
    RCU_PLL = 0x24003010;

    /* Reset HSEBPS bit */
    RCU_CTL &= (uint32_t)0xFFFBFFFF;

    /* Disable all interrupts */
    RCU_INT = 0x00000000;

    /* Configure the System clock source, PLL Multiplier and Divider factors, 
       AHB/APBx prescalers and Flash settings ----------------------------------*/
    SetSysClock();

    /* Configure the Vector Table location add offset address ------------------*/
#ifdef VECT_TAB_SRAM
    SCB->VTOR = SRAM_BASE | VECT_TAB_OFFSET; /* Vector Table Relocation in Internal SRAM */
#else
    SCB->VTOR = FLASH_BASE | VECT_TAB_OFFSET; /* Vector Table Relocation in Internal FLASH */
#endif
}

/**
  * @brief  Update SystemCoreClock variable according to Clock Register Values.
  *         The SystemCoreClock variable contains the core clock (HCLK), it can
  *         be used by the user application to setup the SysTick timer or configure
  *         other parameters.
  *           
  * @note   Each time the core clock (HCLK) changes, this function must be called
  *         to update SystemCoreClock variable value. Otherwise, any configuration
  *         based on this variable will be incorrect.         
  *     
  * @note   - The system frequency computed by this function is not the real 
  *           frequency in the chip. It is calculated based on the predefined
  *           constant and the selected clock source:
  *             
  *           - If SYSCLK source is HSI, SystemCoreClock will contain the HSI_VALUE(*)
  *                                              
  *           - If SYSCLK source is HSE, SystemCoreClock will contain the HSE_VALUE(**)
  *                          
  *           - If SYSCLK source is PLL, SystemCoreClock will contain the HSE_VALUE(**) 
  *             or HSI_VALUE(*) multiplied/divided by the PLL factors.
  *         
  *         (*) HSI_VALUE is a constant defined in gd32f4xx.h file (default value
  *             16 MHz) but the real value may vary depending on the variations
  *             in voltage and temperature.   
  *    
  *         (**) HSE_VALUE is a constant defined in gd32f4xx.h file (default value
  *              25 MHz), user has to ensure that HSE_VALUE is same as the real
  *              frequency of the crystal used. Otherwise, this function may
  *              have wrong result.
  *                
  *         - The result of this function could be not correct when using fractional
  *           value for HSE crystal.
  *     
  * @param  None
  * @retval None
  */
void SystemCoreClockUpdate(void)
{
    uint32_t tmp = 0, pllmf = 0, pllmf4 = 0, pllsel = 0, prediv0 = 0, idx = 0;
    uint32_t ck_freq = 0;

    /* Get SCSS value */
    tmp = RCU_CFG0 & RCU_CFG0_SCSS;

    switch (tmp)
    {
        /* HSI used as system clock */
        case 0x00:
            SystemCoreClock = HSI_VALUE;
            break;
        /* HSE used as system clock */
        case 0x04:
            SystemCoreClock = HSE_VALUE;
            break;
        /* PLL used as system clock */
        case 0x08:
            /* Get PLL clock source and multiplication factor */
            pllmf = RCU_PLL & RCU_PLL_PLLMF;
            pllmf4 = (RCU_PLL & RCU_PLL_PLLMF_4) >> 27;
            pllmf >>= 6;
            if(14 == pllmf){
                pllmf = ((pllmf4 == 0) ? 15 : 16);
            }else if(15 == pllmf){
                pllmf = 16;
            }else{
                pllmf += 2;
            }
            pllsel = (RCU_PLL & RCU_PLL_PLLSEL);
            prediv0 = (RCU_CFG1 & RCU_CFG1_PREDV0) + 1;
            if(RCU_PLLSRC_HSI_IRC16M_DIV2 == pllsel){
                /* HSI oscillator clock divided by 2 selected as PLL clock entry */
                ck_freq = HSI_VALUE / 2;
            }else{
                /* HSE oscillator clock selected as PLL clock entry */
                ck_freq = HSE_VALUE / prediv0;
            }
            SystemCoreClock = ck_freq * pllmf;
            break;
        /* HSI used as system clock */
        default:
            SystemCoreClock = HSI_VALUE;
            break;
    }
    /* Compute HCLK frequency */
    /* Get HCLK prescaler */
    tmp = AHB_PSC_TABLE[((RCU_CFG0 & RCU_CFG0_AHBPSC) >> 4)];
    /* HCLK frequency */
    SystemCoreClock >>= tmp;
}

/**
  * @brief  Configures the System clock source, PLL Multiplier and Divider factors, 
  *         AHB/APBx prescalers and Flash settings
  * @note   This function should be called only once the RCC clock configuration  
  *         is reset to the default reset state (done in SystemInit() function).   
  * @param  None
  * @retval None
  */
static void SetSysClock(void)
{
    /* Enable HSE */
    RCU_CTL |= RCU_CTL_HSEEN;
    
    /* Wait till HSE is ready */
    while((RCU_CTL & RCU_CTL_HSESTB) == 0)
    {
    }
    
    /* Configure the main PLL */
    RCU_PLL = (RCU_PLLSRC_HSE | (25 << 0) | (400 << 6) | (((2) >> 1) << 16) | 
               (((8) >> 1) << 24) | (((8) >> 1) << 27));
    
    /* Enable the main PLL */
    RCU_CTL |= RCU_CTL_PLLEN;
    
    /* Wait till the main PLL is ready */
    while((RCU_CTL & RCU_CTL_PLLSTB) == 0)
    {
    }
    
    /* Configure Flash prefetch, Instruction cache, Data cache and wait state */
    FMC_WS = (FMC_WS & (~FMC_WS_WSCNT)) | FMC_WAIT_STATE_5;
    
    /* Select the main PLL as system clock source */
    RCU_CFG0 &= (uint32_t)((uint32_t)~(RCU_CFG0_SCS));
    RCU_CFG0 |= RCU_CKSYSSRC_PLL;
    
    /* Wait till the main PLL is used as system clock source */
    while ((RCU_CFG0 & (uint32_t)RCU_CFG0_SCSS) != RCU_SCSS_PLL)
    {
    }
    
    /* Configure AHB clock */
    RCU_CFG0 |= RCU_AHB_CKSYS_DIV1;
    
    /* Configure APB2 clock */
    RCU_CFG0 |= RCU_APB2_CKAHB_DIV2;
    
    /* Configure APB1 clock */
    RCU_CFG0 |= RCU_APB1_CKAHB_DIV4;
    
    /* Update SystemCoreClock */
    SystemCoreClock = 200000000;
}

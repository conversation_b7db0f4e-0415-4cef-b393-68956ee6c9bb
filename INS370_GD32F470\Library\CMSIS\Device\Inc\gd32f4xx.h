/**
  ******************************************************************************
  * @file    gd32f4xx.h
  * <AUTHOR> Firmware Team
  * @brief   CMSIS Cortex-M4 Device Peripheral Access Layer Header File
  * @version V1.0.0
  * @date    2024-12-19
  ******************************************************************************
  */

#ifndef GD32F4XX_H
#define GD32F4XX_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "core_cm4.h"
#include "system_gd32f4xx.h"
#include <stdint.h>

/* Exported types ------------------------------------------------------------*/
typedef enum {RESET = 0, SET = !RESET} FlagStatus, ITStatus;
typedef enum {DISABLE = 0, ENABLE = !DISABLE} FunctionalState;
typedef enum {ERROR = 0, SUCCESS = !ERROR} ErrorStatus;

/* Exported constants --------------------------------------------------------*/

/* Configuration of the Cortex-M4 Processor and Core Peripherals */
#define __CM4_REV                 0x0001  /*!< Core revision r0p1                            */
#define __MPU_PRESENT             1       /*!< GD32F4XX provides an MPU                     */
#define __NVIC_PRIO_BITS          4       /*!< GD32F4XX uses 4 Bits for the Priority Levels */
#define __Vendor_SysTickConfig    0       /*!< Set to 1 if different SysTick Config is used */
#define __FPU_PRESENT             1       /*!< FPU present                                   */

/* GD32F4XX Interrupt Number Definition */
typedef enum IRQn
{
/******  Cortex-M4 Processor Exceptions Numbers ****************************************************************/
  NonMaskableInt_IRQn         = -14,    /*!< 2 Non Maskable Interrupt                                          */
  MemoryManagement_IRQn       = -12,    /*!< 4 Cortex-M4 Memory Management Interrupt                          */
  BusFault_IRQn               = -11,    /*!< 5 Cortex-M4 Bus Fault Interrupt                                  */
  UsageFault_IRQn             = -10,    /*!< 6 Cortex-M4 Usage Fault Interrupt                                */
  SVCall_IRQn                 = -5,     /*!< 11 Cortex-M4 SV Call Interrupt                                   */
  DebugMonitor_IRQn           = -4,     /*!< 12 Cortex-M4 Debug Monitor Interrupt                             */
  PendSV_IRQn                 = -2,     /*!< 14 Cortex-M4 Pend SV Interrupt                                   */
  SysTick_IRQn                = -1,     /*!< 15 Cortex-M4 System Tick Interrupt                               */
/******  GD32F4XX specific Interrupt Numbers ********************************************************************/
  WWDGT_IRQn                  = 0,      /*!< Window WatchDog Timer interrupt                                   */
  LVD_IRQn                    = 1,      /*!< LVD through EXTI Line detection interrupt                        */
  TAMPER_IRQn                 = 2,      /*!< Tamper through EXTI Line detection                               */
  RTC_IRQn                    = 3,      /*!< RTC through EXTI Line interrupt                                  */
  FMC_IRQn                    = 4,      /*!< FMC interrupt                                                     */
  RCU_CTC_IRQn                = 5,      /*!< RCU and CTC interrupt                                             */
  EXTI0_IRQn                  = 6,      /*!< EXTI Line0 interrupt                                              */
  EXTI1_IRQn                  = 7,      /*!< EXTI Line1 interrupt                                              */
  EXTI2_IRQn                  = 8,      /*!< EXTI Line2 interrupt                                              */
  EXTI3_IRQn                  = 9,      /*!< EXTI Line3 interrupt                                              */
  EXTI4_IRQn                  = 10,     /*!< EXTI Line4 interrupt                                              */
  DMA0_Channel0_IRQn          = 11,     /*!< DMA0 Channel 0 global interrupt                                  */
  DMA0_Channel1_IRQn          = 12,     /*!< DMA0 Channel 1 global interrupt                                  */
  DMA0_Channel2_IRQn          = 13,     /*!< DMA0 Channel 2 global interrupt                                  */
  DMA0_Channel3_IRQn          = 14,     /*!< DMA0 Channel 3 global interrupt                                  */
  DMA0_Channel4_IRQn          = 15,     /*!< DMA0 Channel 4 global interrupt                                  */
  DMA0_Channel5_IRQn          = 16,     /*!< DMA0 Channel 5 global interrupt                                  */
  DMA0_Channel6_IRQn          = 17,     /*!< DMA0 Channel 6 global interrupt                                  */
  ADC_IRQn                    = 18,     /*!< ADC1, ADC2 and ADC3 global interrupt                             */
  CAN0_TX_IRQn                = 19,     /*!< CAN0 TX interrupt                                                 */
  CAN0_RX0_IRQn               = 20,     /*!< CAN0 RX0 interrupt                                                */
  CAN0_RX1_IRQn               = 21,     /*!< CAN0 RX1 interrupt                                                */
  CAN0_EWMC_IRQn              = 22,     /*!< CAN0 EWMC interrupt                                               */
  EXTI5_9_IRQn                = 23,     /*!< External Line[9:5] interrupt                                      */
  TIMER0_BRK_TIMER8_IRQn      = 24,     /*!< TIMER0 Break and TIMER8 interrupt                                */
  TIMER0_UP_TIMER9_IRQn       = 25,     /*!< TIMER0 Update and TIMER9 interrupt                               */
  TIMER0_TRG_CMT_TIMER10_IRQn = 26,     /*!< TIMER0 Trigger and Commutation and TIMER10 interrupt             */
  TIMER0_Channel_IRQn         = 27,     /*!< TIMER0 Channel Capture Compare interrupt                         */
  TIMER1_IRQn                 = 28,     /*!< TIMER1 global interrupt                                           */
  TIMER2_IRQn                 = 29,     /*!< TIMER2 global interrupt                                           */
  TIMER3_IRQn                 = 30,     /*!< TIMER3 global interrupt                                           */
  I2C0_EV_IRQn                = 31,     /*!< I2C0 Event interrupt                                              */
  I2C0_ER_IRQn                = 32,     /*!< I2C0 Error interrupt                                              */
  I2C1_EV_IRQn                = 33,     /*!< I2C1 Event interrupt                                              */
  I2C1_ER_IRQn                = 34,     /*!< I2C1 Error interrupt                                              */
  SPI0_IRQn                   = 35,     /*!< SPI0 global interrupt                                             */
  SPI1_IRQn                   = 36,     /*!< SPI1 global interrupt                                             */
  USART0_IRQn                 = 37,     /*!< USART0 global interrupt                                           */
  USART1_IRQn                 = 38,     /*!< USART1 global interrupt                                           */
  USART2_IRQn                 = 39,     /*!< USART2 global interrupt                                           */
  EXTI10_15_IRQn              = 40,     /*!< External Line[15:10] interrupt                                    */
  RTC_Alarm_IRQn              = 41,     /*!< RTC Alarm (A and B) through EXTI Line interrupt                  */
  USBFS_WKUP_IRQn             = 42,     /*!< USB OTG FS Wakeup through EXTI line interrupt                    */
  TIMER7_BRK_TIMER11_IRQn     = 43,     /*!< TIMER7 Break and TIMER11 interrupt                               */
  TIMER7_UP_TIMER12_IRQn      = 44,     /*!< TIMER7 Update and TIMER12 interrupt                              */
  TIMER7_TRG_CMT_TIMER13_IRQn = 45,     /*!< TIMER7 Trigger and Commutation and TIMER13 interrupt             */
  TIMER7_Channel_IRQn         = 46,     /*!< TIMER7 Channel Capture Compare interrupt                         */
  DMA0_Channel7_IRQn          = 47,     /*!< DMA0 Channel7 interrupt                                           */
} IRQn_Type;

/* Memory mapping of Cortex-M4 Hardware */
#define FLASH_BASE            0x08000000UL /*!< FLASH(up to 3 MB) base address in the alias region                             */
#define SRAM_BASE             0x20000000UL /*!< SRAM(256 KB) base address in the alias region                                  */
#define PERIPH_BASE           0x40000000UL /*!< Peripheral base address in the alias region                                    */
#define SRAM_BB_BASE          0x22000000UL /*!< SRAM base address in the bit-band region                                       */
#define PERIPH_BB_BASE        0x42000000UL /*!< Peripheral base address in the bit-band region                                 */

/* Peripheral memory map */
#define APB1PERIPH_BASE       PERIPH_BASE
#define APB2PERIPH_BASE       (PERIPH_BASE + 0x00010000UL)
#define AHB1PERIPH_BASE       (PERIPH_BASE + 0x00020000UL)
#define AHB2PERIPH_BASE       (PERIPH_BASE + 0x10000000UL)

/* APB1 peripherals */
#define TIMER1_BASE           (APB1PERIPH_BASE + 0x0000UL)
#define TIMER2_BASE           (APB1PERIPH_BASE + 0x0400UL)
#define TIMER3_BASE           (APB1PERIPH_BASE + 0x0800UL)
#define RTC_BASE              (APB1PERIPH_BASE + 0x2800UL)
#define WWDGT_BASE            (APB1PERIPH_BASE + 0x2C00UL)
#define FWDGT_BASE            (APB1PERIPH_BASE + 0x3000UL)
#define SPI1_BASE             (APB1PERIPH_BASE + 0x3800UL)
#define USART1_BASE           (APB1PERIPH_BASE + 0x4400UL)
#define USART2_BASE           (APB1PERIPH_BASE + 0x4800UL)
#define I2C0_BASE             (APB1PERIPH_BASE + 0x5400UL)
#define I2C1_BASE             (APB1PERIPH_BASE + 0x5800UL)
#define CAN0_BASE             (APB1PERIPH_BASE + 0x6400UL)
#define PMU_BASE              (APB1PERIPH_BASE + 0x7000UL)

/* APB2 peripherals */
#define TIMER0_BASE           (APB2PERIPH_BASE + 0x0000UL)
#define TIMER7_BASE           (APB2PERIPH_BASE + 0x0400UL)
#define USART0_BASE           (APB2PERIPH_BASE + 0x1000UL)
#define ADC0_BASE             (APB2PERIPH_BASE + 0x2000UL)
#define SPI0_BASE             (APB2PERIPH_BASE + 0x3000UL)
#define SYSCFG_BASE           (APB2PERIPH_BASE + 0x3800UL)
#define EXTI_BASE             (APB2PERIPH_BASE + 0x3C00UL)

/* AHB1 peripherals */
#define GPIOA_BASE            (AHB1PERIPH_BASE + 0x0000UL)
#define GPIOB_BASE            (AHB1PERIPH_BASE + 0x0400UL)
#define GPIOC_BASE            (AHB1PERIPH_BASE + 0x0800UL)
#define GPIOD_BASE            (AHB1PERIPH_BASE + 0x0C00UL)
#define GPIOE_BASE            (AHB1PERIPH_BASE + 0x1000UL)
#define GPIOF_BASE            (AHB1PERIPH_BASE + 0x1400UL)
#define GPIOG_BASE            (AHB1PERIPH_BASE + 0x1800UL)
#define GPIOH_BASE            (AHB1PERIPH_BASE + 0x1C00UL)
#define GPIOI_BASE            (AHB1PERIPH_BASE + 0x2000UL)
#define CRC_BASE              (AHB1PERIPH_BASE + 0x3000UL)
#define RCU_BASE              (AHB1PERIPH_BASE + 0x3800UL)
#define FMC_BASE              (AHB1PERIPH_BASE + 0x3C00UL)
#define DMA0_BASE             (AHB1PERIPH_BASE + 0x6000UL)
#define DMA1_BASE             (AHB1PERIPH_BASE + 0x6400UL)

/* Peripheral declarations */
#define GPIOA                 ((GPIO_TypeDef *) GPIOA_BASE)
#define GPIOB                 ((GPIO_TypeDef *) GPIOB_BASE)
#define GPIOC                 ((GPIO_TypeDef *) GPIOC_BASE)
#define GPIOD                 ((GPIO_TypeDef *) GPIOD_BASE)
#define GPIOE                 ((GPIO_TypeDef *) GPIOE_BASE)
#define GPIOF                 ((GPIO_TypeDef *) GPIOF_BASE)
#define GPIOG                 ((GPIO_TypeDef *) GPIOG_BASE)
#define GPIOH                 ((GPIO_TypeDef *) GPIOH_BASE)
#define GPIOI                 ((GPIO_TypeDef *) GPIOI_BASE)

/* GPIO register structure */
typedef struct
{
  __IO uint32_t CTL;      /*!< GPIO port control register,               Address offset: 0x00      */
  __IO uint32_t OMODE;    /*!< GPIO port output mode register,           Address offset: 0x04      */
  __IO uint32_t OSPD;     /*!< GPIO port output speed register,          Address offset: 0x08      */
  __IO uint32_t PUD;      /*!< GPIO port pull-up/pull-down register,     Address offset: 0x0C      */
  __IO uint32_t ISTAT;    /*!< GPIO port input data register,            Address offset: 0x10      */
  __IO uint32_t OCTL;     /*!< GPIO port output data register,           Address offset: 0x14      */
  __IO uint32_t BOP;      /*!< GPIO port bit operation register,         Address offset: 0x18      */
  __IO uint32_t LOCK;     /*!< GPIO port configuration lock register,    Address offset: 0x1C      */
  __IO uint32_t AFSEL0;   /*!< GPIO alternate function selected register, Address offset: 0x20-0x24 */
  __IO uint32_t AFSEL1;   /*!< GPIO alternate function selected register, Address offset: 0x20-0x24 */
} GPIO_TypeDef;

/* Basic constants */
#define HSI_VALUE    ((uint32_t)16000000) /*!< Value of the Internal oscillator in Hz*/
#define HSE_VALUE    ((uint32_t)25000000) /*!< Value of the External oscillator in Hz */

/* GPIO pins */
#define GPIO_PIN_0                 ((uint16_t)0x0001)  /* Pin 0 selected    */
#define GPIO_PIN_1                 ((uint16_t)0x0002)  /* Pin 1 selected    */
#define GPIO_PIN_2                 ((uint16_t)0x0004)  /* Pin 2 selected    */
#define GPIO_PIN_3                 ((uint16_t)0x0008)  /* Pin 3 selected    */
#define GPIO_PIN_4                 ((uint16_t)0x0010)  /* Pin 4 selected    */
#define GPIO_PIN_5                 ((uint16_t)0x0020)  /* Pin 5 selected    */
#define GPIO_PIN_6                 ((uint16_t)0x0040)  /* Pin 6 selected    */
#define GPIO_PIN_7                 ((uint16_t)0x0080)  /* Pin 7 selected    */
#define GPIO_PIN_8                 ((uint16_t)0x0100)  /* Pin 8 selected    */
#define GPIO_PIN_9                 ((uint16_t)0x0200)  /* Pin 9 selected    */
#define GPIO_PIN_10                ((uint16_t)0x0400)  /* Pin 10 selected   */
#define GPIO_PIN_11                ((uint16_t)0x0800)  /* Pin 11 selected   */
#define GPIO_PIN_12                ((uint16_t)0x1000)  /* Pin 12 selected   */
#define GPIO_PIN_13                ((uint16_t)0x2000)  /* Pin 13 selected   */
#define GPIO_PIN_14                ((uint16_t)0x4000)  /* Pin 14 selected   */
#define GPIO_PIN_15                ((uint16_t)0x8000)  /* Pin 15 selected   */

/* GPIO modes */
#define GPIO_MODE_INPUT            0x00000000U   /*!< Input Floating Mode                   */
#define GPIO_MODE_OUTPUT_PP        0x00000001U   /*!< Output Push Pull Mode                 */
#define GPIO_MODE_OUTPUT_OD        0x00000011U   /*!< Output Open Drain Mode                */
#define GPIO_MODE_AF_PP            0x00000002U   /*!< Alternate Function Push Pull Mode     */
#define GPIO_MODE_AF_OD            0x00000012U   /*!< Alternate Function Open Drain Mode    */
#define GPIO_MODE_ANALOG           0x00000003U   /*!< Analog Mode  */

/* GPIO pull */
#define GPIO_NOPULL                0x00000000U   /*!< No Pull-up or Pull-down activation  */
#define GPIO_PULLUP                0x00000001U   /*!< Pull-up activation                  */
#define GPIO_PULLDOWN              0x00000002U   /*!< Pull-down activation                */

/* GPIO speed */
#define GPIO_SPEED_FREQ_LOW        0x00000000U  /*!< IO works at 2 MHz, please refer to the product datasheet */
#define GPIO_SPEED_FREQ_MEDIUM     0x00000001U  /*!< range 12,5 MHz to 50 MHz, please refer to the product datasheet */
#define GPIO_SPEED_FREQ_HIGH       0x00000002U  /*!< range 25 MHz to 100 MHz, please refer to the product datasheet  */
#define GPIO_SPEED_FREQ_VERY_HIGH  0x00000003U  /*!< range 50 MHz to 200 MHz, please refer to the product datasheet  */

/* Exported functions --------------------------------------------------------*/
extern uint32_t SystemCoreClock;          /*!< System Clock Frequency (Core Clock)  */

void SystemInit(void);
void SystemCoreClockUpdate(void);

/* Basic GPIO functions (simplified) */
static inline void gpio_bit_set(uint32_t gpio_periph, uint32_t pin)
{
    GPIO_TypeDef* GPIOx = (GPIO_TypeDef*)gpio_periph;
    GPIOx->BOP = pin;
}

static inline void gpio_bit_reset(uint32_t gpio_periph, uint32_t pin)
{
    GPIO_TypeDef* GPIOx = (GPIO_TypeDef*)gpio_periph;
    GPIOx->BOP = (pin << 16);
}

static inline void gpio_bit_toggle(uint32_t gpio_periph, uint32_t pin)
{
    GPIO_TypeDef* GPIOx = (GPIO_TypeDef*)gpio_periph;
    GPIOx->OCTL ^= pin;
}

static inline FlagStatus gpio_input_bit_get(uint32_t gpio_periph, uint32_t pin)
{
    GPIO_TypeDef* GPIOx = (GPIO_TypeDef*)gpio_periph;
    return (GPIOx->ISTAT & pin) ? SET : RESET;
}

/* Placeholder functions for compilation */
static inline void rcu_periph_clock_enable(uint32_t periph) { /* TODO */ }
static inline void rcu_periph_clock_disable(uint32_t periph) { /* TODO */ }
static inline void rcu_periph_reset_enable(uint32_t periph) { /* TODO */ }
static inline void rcu_periph_reset_disable(uint32_t periph) { /* TODO */ }
static inline void gpio_mode_set(uint32_t gpio_periph, uint32_t mode, uint32_t pull_up_down, uint32_t pin) { /* TODO */ }
static inline void gpio_output_options_set(uint32_t gpio_periph, uint32_t otype, uint32_t speed, uint32_t pin) { /* TODO */ }
static inline void gpio_af_set(uint32_t gpio_periph, uint32_t alt_func_num, uint32_t pin) { /* TODO */ }

/* RCU constants (placeholders) */
#define RCU_GPIOA    0
#define RCU_GPIOB    1
#define RCU_GPIOC    2
#define RCU_GPIOD    3
#define RCU_GPIOE    4
#define RCU_GPIOF    5
#define RCU_GPIOG    6
#define RCU_GPIOH    7
#define RCU_GPIOI    8

#define RCU_GPIOARST 0
#define RCU_GPIOBRST 1
#define RCU_GPIOCRST 2

#ifdef __cplusplus
}
#endif

#endif /* GD32F4XX_H */

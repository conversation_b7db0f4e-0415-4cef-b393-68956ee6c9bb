@echo off
REM ============================================================================
REM Build script for INS370_GD32F470 project
REM ============================================================================

echo ========================================
echo INS370_GD32F470 Build Script
echo ========================================

REM Configuration
set PROJECT_NAME=INS370_GD32F470
set PROJECT_FILE=Project\%PROJECT_NAME%.uvprojx
set KEIL_PATH=C:\Keil_v5\UV4\UV4.exe
set OUTPUT_DIR=Output\Debug

REM Check if Keil5 exists
if not exist "%KEIL_PATH%" (
    echo ERROR: Keil5 not found at %KEIL_PATH%
    echo Please install Keil MDK or update the path
    pause
    exit /b 1
)

REM Check if project file exists
if not exist "%PROJECT_FILE%" (
    echo ERROR: Project file not found: %PROJECT_FILE%
    echo Please ensure you are in the correct directory
    pause
    exit /b 1
)

echo Keil5 found: %KEIL_PATH%
echo Project file: %PROJECT_FILE%
echo Output directory: %OUTPUT_DIR%
echo.

REM Create output directory if it doesn't exist
if not exist "%OUTPUT_DIR%" (
    mkdir "%OUTPUT_DIR%"
    echo Created output directory: %OUTPUT_DIR%
)

REM Clean previous build (optional)
if "%1"=="clean" (
    echo Cleaning previous build...
    if exist "%OUTPUT_DIR%\*.o" del /q "%OUTPUT_DIR%\*.o"
    if exist "%OUTPUT_DIR%\*.d" del /q "%OUTPUT_DIR%\*.d"
    if exist "%OUTPUT_DIR%\*.crf" del /q "%OUTPUT_DIR%\*.crf"
    if exist "%OUTPUT_DIR%\*.htm" del /q "%OUTPUT_DIR%\*.htm"
    if exist "%OUTPUT_DIR%\*.map" del /q "%OUTPUT_DIR%\*.map"
    if exist "%OUTPUT_DIR%\*.axf" del /q "%OUTPUT_DIR%\*.axf"
    if exist "%OUTPUT_DIR%\*.hex" del /q "%OUTPUT_DIR%\*.hex"
    if exist "%OUTPUT_DIR%\*.bin" del /q "%OUTPUT_DIR%\*.bin"
    echo Clean completed.
    echo.
)

REM Kill any existing UV4 processes
echo Terminating existing Keil processes...
taskkill /F /IM UV4.exe >nul 2>&1
timeout /t 1 >nul

REM Start build
echo Starting build...
echo Time: %TIME%
echo.

REM Execute Keil build
"%KEIL_PATH%" -b "%PROJECT_FILE%" -j1 -o "%OUTPUT_DIR%\build.log"

REM Check build result
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo BUILD SUCCESSFUL!
    echo ========================================
    echo Time: %TIME%
    
    REM Check output files
    if exist "%OUTPUT_DIR%\%PROJECT_NAME%.hex" (
        for %%A in ("%OUTPUT_DIR%\%PROJECT_NAME%.hex") do (
            echo HEX file: %%~nxA (%%~zA bytes)
        )
    )
    
    if exist "%OUTPUT_DIR%\%PROJECT_NAME%.bin" (
        for %%A in ("%OUTPUT_DIR%\%PROJECT_NAME%.bin") do (
            echo BIN file: %%~nxA (%%~zA bytes)
        )
    )
    
    if exist "%OUTPUT_DIR%\%PROJECT_NAME%.axf" (
        for %%A in ("%OUTPUT_DIR%\%PROJECT_NAME%.axf") do (
            echo AXF file: %%~nxA (%%~zA bytes)
        )
    )
    
    echo.
    echo Build artifacts saved to: %OUTPUT_DIR%
    echo Ready for deployment!
    
) else (
    echo.
    echo ========================================
    echo BUILD FAILED!
    echo ========================================
    echo Error code: %ERRORLEVEL%
    echo.
    
    REM Show build log if available
    if exist "%OUTPUT_DIR%\build.log" (
        echo Build log contents:
        echo ----------------------------------------
        type "%OUTPUT_DIR%\build.log"
        echo ----------------------------------------
    )
    
    echo.
    echo Please check the build log for details.
)

echo.
echo Build script completed.
if "%1" NEQ "auto" pause

/**
  ******************************************************************************
  * @file    app_navigation.h
  * <AUTHOR> Team
  * @brief   Header file for navigation application module
  * @version V1.0.0
  * @date    2024-12-19
  ******************************************************************************
  */

#ifndef __APP_NAVIGATION_H
#define __APP_NAVIGATION_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "gd32f4xx.h"
#include "project_config.h"
#include <stdint.h>
#include <stdbool.h>
#include <math.h>

/* Exported types ------------------------------------------------------------*/

/* Navigation state enumeration */
typedef enum {
    NAV_STATE_INIT = 0,
    NAV_STATE_ALIGNMENT,
    NAV_STATE_NAVIGATION,
    NAV_STATE_CALIBRATION,
    NAV_STATE_ERROR
} nav_state_t;

/* IMU data structure */
typedef struct {
    float gyro[3];              // Gyroscope data (rad/s)
    float accel[3];             // Accelerometer data (m/s²)
    float mag[3];               // Magnetometer data (Gauss)
    float temperature;          // Temperature (°C)
    uint32_t timestamp;         // Timestamp (ms)
    bool valid;                 // Data validity flag
} imu_data_t;

/* GPS data structure */
typedef struct {
    double latitude;            // Latitude (degrees)
    double longitude;           // Longitude (degrees)
    double altitude;            // Altitude (meters)
    float velocity_n;           // North velocity (m/s)
    float velocity_e;           // East velocity (m/s)
    float velocity_d;           // Down velocity (m/s)
    float heading;              // Heading (degrees)
    uint8_t satellites;         // Number of satellites
    uint8_t fix_type;           // GPS fix type
    float hdop;                 // Horizontal dilution of precision
    uint32_t timestamp;         // Timestamp (ms)
    bool valid;                 // Data validity flag
} gps_data_t;

/* Navigation output structure */
typedef struct {
    /* Position */
    double latitude;            // Latitude (degrees)
    double longitude;           // Longitude (degrees)
    double altitude;            // Altitude (meters)
    
    /* Velocity */
    float velocity_n;           // North velocity (m/s)
    float velocity_e;           // East velocity (m/s)
    float velocity_d;           // Down velocity (m/s)
    
    /* Attitude */
    float roll;                 // Roll angle (degrees)
    float pitch;                // Pitch angle (degrees)
    float yaw;                  // Yaw angle (degrees)
    
    /* Angular rates */
    float gyro_x;               // X-axis angular rate (deg/s)
    float gyro_y;               // Y-axis angular rate (deg/s)
    float gyro_z;               // Z-axis angular rate (deg/s)
    
    /* Accelerations */
    float accel_x;              // X-axis acceleration (m/s²)
    float accel_y;              // Y-axis acceleration (m/s²)
    float accel_z;              // Z-axis acceleration (m/s²)
    
    /* Status and quality */
    nav_state_t state;          // Navigation state
    uint8_t solution_status;    // Solution status
    uint8_t position_type;      // Position type
    float position_std[3];      // Position standard deviation
    float velocity_std[3];      // Velocity standard deviation
    float attitude_std[3];      // Attitude standard deviation
    
    /* Timing */
    uint32_t gps_week;          // GPS week
    double gps_time;            // GPS time of week (seconds)
    uint32_t timestamp;         // System timestamp (ms)
    
} nav_output_t;

/* Navigation statistics */
typedef struct {
    uint32_t update_count;      // Total update count
    uint32_t imu_count;         // IMU data count
    uint32_t gps_count;         // GPS data count
    uint32_t error_count;       // Error count
    float avg_update_time;      // Average update time (ms)
    float max_update_time;      // Maximum update time (ms)
} nav_statistics_t;

/* Exported constants --------------------------------------------------------*/
#define NAV_MAX_SATELLITES      32
#define NAV_ALIGNMENT_TIME_S    60      // Alignment time in seconds
#define NAV_UPDATE_RATE_HZ      200     // Navigation update rate
#define NAV_IMU_RATE_HZ         1000    // IMU data rate
#define NAV_GPS_RATE_HZ         10      // GPS data rate

/* GPS fix types */
#define GPS_FIX_NONE            0
#define GPS_FIX_2D              1
#define GPS_FIX_3D              2
#define GPS_FIX_DGPS            3
#define GPS_FIX_RTK_FLOAT       4
#define GPS_FIX_RTK_FIXED       5

/* Solution status */
#define SOL_STATUS_NONE         0
#define SOL_STATUS_ALIGNMENT    1
#define SOL_STATUS_COARSE       2
#define SOL_STATUS_FINE         3
#define SOL_STATUS_RTK          4

/* Exported functions --------------------------------------------------------*/

/* Initialization and configuration */
void App_Navigation_Init(void);
void App_Navigation_DeInit(void);
void App_Navigation_Reset(void);
void App_Navigation_Start(void);
void App_Navigation_Stop(void);

/* Main processing functions */
void App_Navigation_Update(void);
void App_Navigation_ProcessIMU(const imu_data_t* imu_data);
void App_Navigation_ProcessGPS(const gps_data_t* gps_data);

/* Data access functions */
nav_output_t* App_Navigation_GetOutput(void);
nav_state_t App_Navigation_GetState(void);
nav_statistics_t* App_Navigation_GetStatistics(void);

/* Configuration functions */
void App_Navigation_SetInitialPosition(double lat, double lon, double alt);
void App_Navigation_SetInitialAttitude(float roll, float pitch, float yaw);
void App_Navigation_SetUpdateRate(uint32_t rate_hz);

/* Calibration functions */
void App_Navigation_StartCalibration(void);
void App_Navigation_StopCalibration(void);
bool App_Navigation_IsCalibrationComplete(void);

/* Alignment functions */
void App_Navigation_StartAlignment(void);
void App_Navigation_StopAlignment(void);
bool App_Navigation_IsAlignmentComplete(void);
float App_Navigation_GetAlignmentProgress(void);

/* Quality assessment */
bool App_Navigation_IsPositionValid(void);
bool App_Navigation_IsVelocityValid(void);
bool App_Navigation_IsAttitudeValid(void);
float App_Navigation_GetPositionAccuracy(void);
float App_Navigation_GetVelocityAccuracy(void);
float App_Navigation_GetAttitudeAccuracy(void);

/* Utility functions */
void App_Navigation_PrintStatus(void);
void App_Navigation_PrintStatistics(void);
uint32_t App_Navigation_GetUptime(void);

/* Coordinate transformation functions */
void App_Navigation_LLA2ECEF(double lat, double lon, double alt, double* ecef);
void App_Navigation_ECEF2LLA(const double* ecef, double* lat, double* lon, double* alt);
void App_Navigation_LLA2NED(double lat, double lon, double alt, 
                           double ref_lat, double ref_lon, double ref_alt, 
                           float* ned);

/* Legacy compatibility functions (for HPM6750 porting) */
#define INS_Navigation_Init()           App_Navigation_Init()
#define INS_Navigation_Update()         App_Navigation_Update()
#define INS_Navigation_GetOutput()      App_Navigation_GetOutput()
#define INS_Navigation_ProcessIMU(imu)  App_Navigation_ProcessIMU(imu)
#define INS_Navigation_ProcessGPS(gps)  App_Navigation_ProcessGPS(gps)

#ifdef __cplusplus
}
#endif

#endif /* __APP_NAVIGATION_H */

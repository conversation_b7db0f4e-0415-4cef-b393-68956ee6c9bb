/**
  ******************************************************************************
  * @file    systick.c
  * <AUTHOR> Team
  * @brief   Systick module implementation
  * @version V1.0.0
  * @date    2024-12-19
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "systick.h"

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
static volatile uint32_t delay_time = 0;

/* Private function prototypes -----------------------------------------------*/
/* Private functions ---------------------------------------------------------*/

/**
  * @brief  Configure systick
  * @param  None
  * @retval None
  */
void systick_config(void)
{
    /* Setup SysTick Timer for 1 msec interrupts */
    if (SysTick_Config(SystemCoreClock / 1000))
    {
        /* Capture error */
        while (1);
    }
    
    /* Configure the SysTick handler priority */
    NVIC_SetPriority(SysTick_IRQn, 0x0);
}

/**
  * @brief  Delay function
  * @param  count: delay time in milliseconds
  * @retval None
  */
void delay_1ms(uint32_t count)
{
    delay_time = count;
    
    while(0 != delay_time);
}

/**
  * @brief  Decrement delay time
  * @param  None
  * @retval None
  */
void delay_decrement(void)
{
    if (0 != delay_time)
    {
        delay_time--;
    }
}

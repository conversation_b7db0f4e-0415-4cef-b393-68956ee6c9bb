/**
  ******************************************************************************
  * @file    main.c
  * <AUTHOR> Team
  * @brief   Main program body
  * @version V1.0.0
  * @date    2024-12-19
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2024 INS370 Project.
  * All rights reserved.
  *
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "main.h"

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
static system_status_t system_status;
static uint32_t system_tick_counter = 0;

/* Private function prototypes -----------------------------------------------*/
static void System_Hardware_Init(void);
static void System_Peripheral_Init(void);
static void System_Application_Init(void);
static void System_Main_Loop(void);

/* Private functions ---------------------------------------------------------*/

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{
    /* Initialize system */
    System_Init();
    
    /* Print startup information */
    INFO_PRINTF("===========================================");
    INFO_PRINTF("INS370 GD32F470 System Starting...");
    INFO_PRINTF("Firmware Version: %s", FIRMWARE_VERSION_STRING);
    INFO_PRINTF("Build Date: %s %s", BUILD_DATE, BUILD_TIME);
    INFO_PRINTF("MCU: GD32F470VGT6 @ %d MHz", SYSTEM_CLOCK_FREQ / 1000000);
    INFO_PRINTF("===========================================");
    
    /* Set system state to ready */
    System_SetState(SYSTEM_STATE_READY);
    
    /* Start main loop */
    System_Main_Loop();
    
    /* Should never reach here */
    while (1)
    {
        System_Error_Handler();
    }
}

/**
  * @brief  System initialization
  * @retval None
  */
void System_Init(void)
{
    /* Initialize system status */
    memset(&system_status, 0, sizeof(system_status));
    system_status.state = SYSTEM_STATE_INIT;
    system_status.timestamp = 0;
    
    /* Configure system clock */
    SystemClock_Config();
    
    /* Initialize hardware */
    System_Hardware_Init();
    
    /* Initialize peripherals */
    System_Peripheral_Init();
    
    /* Initialize applications */
    System_Application_Init();
    
    INFO_PRINTF("System initialization completed");
}

/**
  * @brief  Hardware initialization
  * @retval None
  */
static void System_Hardware_Init(void)
{
    /* Initialize system tick */
    systick_config();
    
    /* Initialize GPIO */
    HAL_GPIO_Init();
    
    /* Initialize LEDs */
    BSP_LED_Init();
    
    /* Initialize buttons */
    BSP_Button_Init();
    
    /* Turn on power LED */
    BSP_LED_On(LED1);
    
    DEBUG_PRINTF("Hardware initialization completed");
}

/**
  * @brief  Peripheral initialization
  * @retval None
  */
static void System_Peripheral_Init(void)
{
    /* Initialize UART */
    HAL_UART_Init();
    
    /* Initialize SPI */
    HAL_SPI_Init();
    
    /* Initialize I2C */
    HAL_I2C_Init();
    
    /* Initialize CAN */
    HAL_CAN_Init();
    
    /* Initialize Timer */
    HAL_Timer_Init();
    
    /* Initialize ADC */
    HAL_ADC_Init();
    
    /* Initialize Flash */
    HAL_Flash_Init();
    
    DEBUG_PRINTF("Peripheral initialization completed");
}

/**
  * @brief  Application initialization
  * @retval None
  */
static void System_Application_Init(void)
{
    /* Initialize BSP layer */
    BSP_Sensor_Init();
    BSP_Communication_Init();
    
    /* Initialize application modules */
    App_Navigation_Init();
    App_Communication_Init();
    App_DataProcess_Init();
    App_Calibration_Init();
    App_System_Init();
    
    /* Initialize middleware */
    Middleware_Protocol_Init();
    
    /* Initialize ported HPM6750 functions */
    Ported_SetParaBao_Init();
    Ported_Communication_Init();
    Ported_Flash_Init();
    Ported_Integration_Init();
    
    /* Update system status */
    system_status.navigation_ready = true;
    system_status.communication_ready = true;
    system_status.sensor_ready = true;
    
    DEBUG_PRINTF("Application initialization completed");
}

/**
  * @brief  Main system loop
  * @retval None
  */
static void System_Main_Loop(void)
{
    uint32_t nav_tick = 0;
    uint32_t comm_tick = 0;
    uint32_t led_tick = 0;
    
    System_SetState(SYSTEM_STATE_RUNNING);
    INFO_PRINTF("System running...");
    
    while (1)
    {
        uint32_t current_tick = System_GetTick();
        
        /* Navigation update at 200Hz */
        if (current_tick - nav_tick >= (1000 / NAVIGATION_FREQ))
        {
            nav_tick = current_tick;
            App_Navigation_Update();
        }
        
        /* Communication update at 100Hz */
        if (current_tick - comm_tick >= (1000 / COMMUNICATION_FREQ))
        {
            comm_tick = current_tick;
            App_Communication_Update();
        }
        
        /* LED heartbeat at 1Hz */
        if (current_tick - led_tick >= 1000)
        {
            led_tick = current_tick;
            BSP_LED_Toggle(LED2);
        }
        
        /* Process data */
        App_DataProcess_Update();
        
        /* Handle system tasks */
        App_System_Update();
        
        /* Process ported HPM6750 functions */
        Ported_Integration_Update();
        
        /* Check for errors */
        if (system_status.error_code != 0)
        {
            System_SetState(SYSTEM_STATE_ERROR);
            ERROR_PRINTF("System error detected: 0x%08X", system_status.error_code);
            BSP_LED_On(LED3);  // Error LED
        }
        
        /* Yield to lower priority tasks */
        __WFI();  // Wait for interrupt
    }
}

/**
  * @brief  System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
    /* Configure system clock to 200MHz */
    /* This is a placeholder - actual implementation depends on GD32F470 HAL */
    
    /* Enable HSE */
    rcu_osci_on(RCU_HXTAL);
    rcu_osci_stab_wait(RCU_HXTAL);
    
    /* Configure PLL */
    rcu_pll_config(RCU_PLLSRC_HXTAL, 25, 400, 2, 8, 8);
    rcu_osci_on(RCU_PLL_CK);
    rcu_osci_stab_wait(RCU_PLL_CK);
    
    /* Configure AHB, APB1, APB2 prescalers */
    rcu_ahb_clock_config(RCU_AHB_CKSYS_DIV1);
    rcu_apb1_clock_config(RCU_APB1_CKAHB_DIV4);
    rcu_apb2_clock_config(RCU_APB2_CKAHB_DIV2);
    
    /* Select PLL as system clock */
    rcu_system_clock_source_config(RCU_CKSYSSRC_PLL);
    while(RCU_SCSS_PLL != rcu_system_clock_source_get());
    
    /* Update system core clock */
    SystemCoreClockUpdate();
    
    DEBUG_PRINTF("System clock configured to %d MHz", SystemCoreClock / 1000000);
}

/**
  * @brief  Get system status
  * @retval Pointer to system status structure
  */
system_status_t* System_GetStatus(void)
{
    return &system_status;
}

/**
  * @brief  Set system state
  * @param  new_state: New system state
  * @retval None
  */
void System_SetState(system_state_t new_state)
{
    system_status.state = new_state;
    system_status.timestamp = System_GetTick();
    
    DEBUG_PRINTF("System state changed to: %d", new_state);
}

/**
  * @brief  Get current system state
  * @retval Current system state
  */
system_state_t System_GetState(void)
{
    return system_status.state;
}

/**
  * @brief  Get system tick
  * @retval Current system tick in milliseconds
  */
uint32_t System_GetTick(void)
{
    return system_tick_counter;
}

/**
  * @brief  System delay
  * @param  delay_ms: Delay in milliseconds
  * @retval None
  */
void System_Delay(uint32_t delay_ms)
{
    uint32_t start_tick = System_GetTick();
    while ((System_GetTick() - start_tick) < delay_ms)
    {
        __NOP();
    }
}

/**
  * @brief  System reset
  * @retval None
  */
void System_Reset(void)
{
    INFO_PRINTF("System reset requested");
    NVIC_SystemReset();
}

/**
  * @brief  System error handler
  * @retval None
  */
void System_Error_Handler(void)
{
    ERROR_PRINTF("System error handler called");
    
    /* Turn on error LED */
    BSP_LED_On(LED3);
    
    /* Disable interrupts */
    __disable_irq();
    
    /* Infinite loop */
    while (1)
    {
        BSP_LED_Toggle(LED3);
        for (volatile uint32_t i = 0; i < 1000000; i++);
    }
}

/**
  * @brief  SysTick interrupt handler
  * @retval None
  */
void SysTick_Handler(void)
{
    system_tick_counter++;
}

/**
  * @brief  This function handles Hard Fault exception.
  * @retval None
  */
void HardFault_Handler(void)
{
    ERROR_PRINTF("Hard Fault occurred");
    System_Error_Handler();
}

/**
  * @brief  This function handles Memory Manage exception.
  * @retval None
  */
void MemManage_Handler(void)
{
    ERROR_PRINTF("Memory Management Fault occurred");
    System_Error_Handler();
}

/**
  * @brief  This function handles Bus Fault exception.
  * @retval None
  */
void BusFault_Handler(void)
{
    ERROR_PRINTF("Bus Fault occurred");
    System_Error_Handler();
}

/**
  * @brief  This function handles Usage Fault exception.
  * @retval None
  */
void UsageFault_Handler(void)
{
    ERROR_PRINTF("Usage Fault occurred");
    System_Error_Handler();
}

Write-Host "Setting up modular project structure for INS370_GD32F470..." -ForegroundColor Green

# 创建详细的模块化目录
$ModularDirs = @(
    # 核心系统
    "Source\Core\Inc",
    "Source\Core\Src", 
    "Source\Core\Startup",
    
    # 硬件抽象层
    "Source\HAL\Inc",
    "Source\HAL\Src",
    "Source\HAL\GPIO",
    "Source\HAL\UART",
    "Source\HAL\SPI", 
    "Source\HAL\I2C",
    "Source\HAL\CAN",
    "Source\HAL\Timer",
    "Source\HAL\ADC",
    "Source\HAL\Flash",
    
    # 板级支持包
    "Source\BSP\Inc",
    "Source\BSP\Src",
    "Source\BSP\LED",
    "Source\BSP\Button",
    "Source\BSP\Sensor",
    "Source\BSP\Communication",
    
    # 应用层模块
    "Source\App\Navigation\Inc",
    "Source\App\Navigation\Src",
    "Source\App\Communication\Inc", 
    "Source\App\Communication\Src",
    "Source\App\DataProcess\Inc",
    "Source\App\DataProcess\Src",
    "Source\App\Calibration\Inc",
    "Source\App\Calibration\Src",
    "Source\App\System\Inc",
    "Source\App\System\Src",
    
    # 中间件
    "Source\Middleware\Inc",
    "Source\Middleware\Src",
    "Source\Middleware\RTOS",
    "Source\Middleware\FileSystem",
    "Source\Middleware\Protocol",
    
    # 移植的HPM6750功能
    "Source\Ported\HPM6750\Inc",
    "Source\Ported\HPM6750\Src",
    "Source\Ported\HPM6750\Config",
    "Source\Ported\HPM6750\SetParaBao",
    "Source\Ported\HPM6750\Communication",
    "Source\Ported\HPM6750\Flash",
    "Source\Ported\HPM6750\Integration",
    
    # 库文件
    "Library\GD32F4xx_HAL\Inc",
    "Library\GD32F4xx_HAL\Src", 
    "Library\CMSIS\Device\Inc",
    "Library\CMSIS\Device\Src",
    "Library\CMSIS\Core\Inc",
    
    # 配置文件
    "Config\Inc",
    "Config\Src",
    "Config\Keil",
    "Config\Debug"
)

# 创建目录
foreach ($dir in $ModularDirs) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "Created: $dir" -ForegroundColor Green
    }
}

Write-Host "Modular structure setup completed!" -ForegroundColor Green

/**
  ******************************************************************************
  * @file    empty_declarations.h
  * <AUTHOR> Team
  * @brief   Empty function declarations for initial compilation
  * @version V1.0.0
  * @date    2024-12-19
  ******************************************************************************
  * @attention
  *
  * This file contains empty declarations to allow initial compilation.
  * These should be replaced with actual implementations as modules are developed.
  *
  ******************************************************************************
  */

#ifndef __EMPTY_DECLARATIONS_H
#define __EMPTY_DECLARATIONS_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "gd32f4xx.h"
#include <stdint.h>
#include <stdbool.h>

/* Empty function declarations for HAL layer --------------------------------*/
static inline void HAL_UART_Init(void) { /* TODO: Implement */ }
static inline void HAL_SPI_Init(void) { /* TODO: Implement */ }
static inline void HAL_I2C_Init(void) { /* TODO: Implement */ }
static inline void HAL_CAN_Init(void) { /* TODO: Implement */ }
static inline void HAL_Timer_Init(void) { /* TODO: Implement */ }
static inline void HAL_ADC_Init(void) { /* TODO: Implement */ }
static inline void HAL_Flash_Init(void) { /* TODO: Implement */ }

/* Empty function declarations for BSP layer --------------------------------*/
static inline void BSP_Button_Init(void) { /* TODO: Implement */ }
static inline void BSP_Sensor_Init(void) { /* TODO: Implement */ }
static inline void BSP_Communication_Init(void) { /* TODO: Implement */ }

/* Empty function declarations for Application layer ------------------------*/
static inline void App_Navigation_Init(void) { /* TODO: Implement */ }
static inline void App_Navigation_Update(void) { /* TODO: Implement */ }
static inline void App_Communication_Init(void) { /* TODO: Implement */ }
static inline void App_Communication_Update(void) { /* TODO: Implement */ }
static inline void App_DataProcess_Init(void) { /* TODO: Implement */ }
static inline void App_DataProcess_Update(void) { /* TODO: Implement */ }
static inline void App_Calibration_Init(void) { /* TODO: Implement */ }
static inline void App_System_Init(void) { /* TODO: Implement */ }
static inline void App_System_Update(void) { /* TODO: Implement */ }

/* Empty function declarations for Middleware -------------------------------*/
static inline void Middleware_Protocol_Init(void) { /* TODO: Implement */ }

/* Native GD32F470 specific functions ---------------------------------------*/
/* All functions are implemented natively for GD32F470 platform */

#ifdef __cplusplus
}
#endif

#endif /* __EMPTY_DECLARATIONS_H */

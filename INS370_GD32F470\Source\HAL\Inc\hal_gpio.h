/**
  ******************************************************************************
  * @file    hal_gpio.h
  * <AUTHOR> Team
  * @brief   Header file for GPIO HAL module
  * @version V1.0.0
  * @date    2024-12-19
  ******************************************************************************
  */

#ifndef __HAL_GPIO_H
#define __HAL_GPIO_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "gd32f4xx.h"
#include "project_config.h"
#include "hardware_config.h"

/* Exported types ------------------------------------------------------------*/
typedef enum {
    HAL_GPIO_MODE_INPUT = 0,
    HAL_GPIO_MODE_OUTPUT,
    HAL_GPIO_MODE_AF,
    HAL_GPIO_MODE_ANALOG
} hal_gpio_mode_t;

typedef enum {
    HAL_GPIO_PULL_NONE = 0,
    HAL_GPIO_PULL_UP,
    HAL_GPIO_PULL_DOWN
} hal_gpio_pull_t;

typedef enum {
    HAL_GPIO_SPEED_LOW = 0,
    HAL_GPIO_SPEED_MEDIUM,
    HAL_GPIO_SPEED_HIGH,
    HAL_GPIO_SPEED_VERY_HIGH
} hal_gpio_speed_t;

typedef enum {
    HAL_GPIO_OUTPUT_PP = 0,
    HAL_GPIO_OUTPUT_OD
} hal_gpio_output_t;

typedef struct {
    uint32_t pin;
    hal_gpio_mode_t mode;
    hal_gpio_pull_t pull;
    hal_gpio_speed_t speed;
    hal_gpio_output_t output_type;
    uint32_t alternate;
} hal_gpio_init_t;

/* Exported constants --------------------------------------------------------*/
#define HAL_GPIO_PIN_SET        1
#define HAL_GPIO_PIN_RESET      0

/* Exported macro ------------------------------------------------------------*/
/* Exported functions --------------------------------------------------------*/

/* Initialization and de-initialization functions */
void HAL_GPIO_Init(void);
void HAL_GPIO_DeInit(void);
void HAL_GPIO_Pin_Init(uint32_t port, hal_gpio_init_t* gpio_init);

/* IO operation functions */
void HAL_GPIO_WritePin(uint32_t port, uint32_t pin, uint32_t pin_state);
uint32_t HAL_GPIO_ReadPin(uint32_t port, uint32_t pin);
void HAL_GPIO_TogglePin(uint32_t port, uint32_t pin);

/* Configuration functions */
void HAL_GPIO_ConfigPin(uint32_t port, uint32_t pin, hal_gpio_mode_t mode, hal_gpio_pull_t pull);
void HAL_GPIO_ConfigAlternate(uint32_t port, uint32_t pin, uint32_t alternate);

/* Utility functions */
void HAL_GPIO_EnableClock(uint32_t port);
void HAL_GPIO_DisableClock(uint32_t port);

#ifdef __cplusplus
}
#endif

#endif /* __HAL_GPIO_H */

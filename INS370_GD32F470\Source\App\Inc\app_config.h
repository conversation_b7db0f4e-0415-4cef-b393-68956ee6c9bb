/**
  ******************************************************************************
  * @file    app_config.h
  * <AUTHOR> Team
  * @brief   Application configuration module for GD32F470
  * @version V1.0.0
  * @date    2024-12-19
  ******************************************************************************
  * @attention
  *
  * Native GD32F470 implementation - no legacy compatibility required.
  * Optimized for GD32F470 platform with modern embedded design patterns.
  *
  ******************************************************************************
  */

#ifndef __APP_CONFIG_H
#define __APP_CONFIG_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "gd32f4xx.h"
#include "project_config.h"
#include <stdint.h>
#include <stdbool.h>
#include <string.h>

/* Exported types ------------------------------------------------------------*/

/* System configuration structure */
typedef struct {
    /* System Information */
    uint32_t system_id;         // System identification
    uint32_t hardware_version;  // Hardware version
    uint32_t software_version;  // Software version
    uint32_t serial_number;     // Device serial number
    char device_name[32];       // Device name string
    
    /* Navigation Configuration */
    struct {
        uint32_t update_rate_hz;    // Navigation update rate (Hz)
        uint32_t imu_rate_hz;       // IMU sampling rate (Hz)
        uint32_t gps_rate_hz;       // GPS update rate (Hz)
        bool auto_alignment;        // Enable auto alignment
        uint32_t alignment_time_s;  // Alignment time (seconds)
        
        /* Initial position */
        double init_latitude;       // Initial latitude (degrees)
        double init_longitude;      // Initial longitude (degrees)
        double init_altitude;       // Initial altitude (meters)
        float init_heading;         // Initial heading (degrees)
        
        /* Sensor calibration */
        float gyro_bias[3];         // Gyroscope bias (rad/s)
        float accel_bias[3];        // Accelerometer bias (m/s²)
        float gyro_scale[3];        // Gyroscope scale factor
        float accel_scale[3];       // Accelerometer scale factor
        
        /* Filter parameters */
        float process_noise_gyro;   // Process noise for gyroscope
        float process_noise_accel;  // Process noise for accelerometer
        float measurement_noise_gps; // Measurement noise for GPS
        
    } navigation;
    
    /* Communication Configuration */
    struct {
        /* UART settings */
        uint32_t uart1_baudrate;    // Debug UART baudrate
        uint32_t uart2_baudrate;    // GPS UART baudrate
        uint32_t uart3_baudrate;    // External comm baudrate
        uint32_t uart4_baudrate;    // Data output baudrate
        
        /* Protocol settings */
        uint8_t output_format;      // Output data format
        uint8_t protocol_type;      // Communication protocol
        uint32_t output_rate_hz;    // Output data rate (Hz)
        
        /* CAN settings */
        uint32_t can1_baudrate;     // CAN1 baudrate
        uint32_t can2_baudrate;     // CAN2 baudrate
        bool can1_enabled;          // CAN1 enable flag
        bool can2_enabled;          // CAN2 enable flag
        
    } communication;
    
    /* System Configuration */
    struct {
        bool watchdog_enabled;      // Watchdog enable
        uint32_t watchdog_timeout;  // Watchdog timeout (ms)
        bool low_power_enabled;     // Low power mode enable
        uint32_t sleep_timeout;     // Sleep timeout (ms)
        
        /* Debug settings */
        uint8_t debug_level;        // Debug output level
        bool debug_uart_enabled;    // Debug UART enable
        bool debug_rtt_enabled;     // RTT debug enable
        
        /* Performance monitoring */
        bool perf_monitor_enabled;  // Performance monitor enable
        bool memory_monitor_enabled; // Memory monitor enable
        
    } system;
    
    /* Calibration Configuration */
    struct {
        bool auto_calibration;      // Enable auto calibration
        uint32_t calib_duration_s;  // Calibration duration (seconds)
        float calib_threshold;      // Calibration threshold
        uint32_t calib_samples;     // Number of calibration samples
        
        /* Temperature compensation */
        bool temp_compensation;     // Enable temperature compensation
        float temp_coefficients[6]; // Temperature compensation coefficients
        
    } calibration;
    
    /* Advanced Configuration */
    struct {
        float lever_arm[3];         // Lever arm from IMU to GPS (m)
        float mounting_angles[3];   // Mounting angles (degrees)
        uint8_t coordinate_frame;   // Coordinate frame definition
        
        /* Advanced filter settings */
        bool adaptive_filter;       // Enable adaptive filtering
        float innovation_threshold; // Innovation threshold
        uint32_t outlier_rejection; // Outlier rejection method
        
    } advanced;
    
} app_config_t;

/* Configuration validation result */
typedef enum {
    CONFIG_VALID = 0,
    CONFIG_INVALID_RANGE,
    CONFIG_INVALID_FORMAT,
    CONFIG_FLASH_ERROR,
    CONFIG_CHECKSUM_ERROR,
    CONFIG_VERSION_MISMATCH,
    CONFIG_UNKNOWN_ERROR
} config_result_t;

/* Configuration categories */
typedef enum {
    CONFIG_CATEGORY_SYSTEM = 0,
    CONFIG_CATEGORY_NAVIGATION,
    CONFIG_CATEGORY_COMMUNICATION,
    CONFIG_CATEGORY_CALIBRATION,
    CONFIG_CATEGORY_ADVANCED,
    CONFIG_CATEGORY_ALL
} config_category_t;

/* Exported constants --------------------------------------------------------*/
#define APP_CONFIG_VERSION          0x01000000  // v1.0.0.0
#define APP_CONFIG_MAGIC_NUMBER     0x47443332  // "GD32"
#define APP_CONFIG_MAX_SIZE         4096        // Maximum config size
#define APP_CONFIG_FLASH_SECTOR     15          // Flash sector for storage

/* Default configuration values */
#define DEFAULT_NAV_UPDATE_RATE     200         // Hz
#define DEFAULT_IMU_RATE            1000        // Hz
#define DEFAULT_GPS_RATE            10          // Hz
#define DEFAULT_OUTPUT_RATE         100         // Hz
#define DEFAULT_UART_BAUDRATE       115200      // bps
#define DEFAULT_CAN_BAUDRATE        500000      // bps
#define DEFAULT_ALIGNMENT_TIME      60          // seconds
#define DEFAULT_CALIB_DURATION      30          // seconds

/* Configuration limits */
#define MIN_UPDATE_RATE             1           // Hz
#define MAX_UPDATE_RATE             1000        // Hz
#define MIN_BAUDRATE                9600        // bps
#define MAX_BAUDRATE                921600      // bps
#define MIN_ALIGNMENT_TIME          10          // seconds
#define MAX_ALIGNMENT_TIME          300         // seconds

/* Exported functions --------------------------------------------------------*/

/* Initialization and management */
void App_Config_Init(void);
void App_Config_DeInit(void);
config_result_t App_Config_LoadDefaults(void);
config_result_t App_Config_Reset(void);

/* Configuration access */
config_result_t App_Config_Get(app_config_t* config);
config_result_t App_Config_Set(const app_config_t* config);
config_result_t App_Config_GetCategory(config_category_t category, void* data, uint32_t size);
config_result_t App_Config_SetCategory(config_category_t category, const void* data, uint32_t size);

/* Parameter access by ID */
config_result_t App_Config_GetParameter(uint16_t param_id, void* value, uint16_t size);
config_result_t App_Config_SetParameter(uint16_t param_id, const void* value, uint16_t size);

/* Validation */
config_result_t App_Config_Validate(const app_config_t* config);
config_result_t App_Config_ValidateCategory(config_category_t category, const void* data, uint32_t size);
bool App_Config_IsValid(void);

/* Flash operations */
config_result_t App_Config_SaveToFlash(void);
config_result_t App_Config_LoadFromFlash(void);
config_result_t App_Config_EraseFlash(void);

/* Backup and restore */
config_result_t App_Config_Backup(uint8_t* buffer, uint32_t buffer_size, uint32_t* actual_size);
config_result_t App_Config_Restore(const uint8_t* buffer, uint32_t buffer_size);

/* Utility functions */
uint32_t App_Config_CalculateChecksum(const app_config_t* config);
void App_Config_Print(void);
void App_Config_PrintCategory(config_category_t category);

/* Quick access functions for common parameters */
uint32_t App_Config_GetNavigationRate(void);
void App_Config_SetNavigationRate(uint32_t rate_hz);
uint32_t App_Config_GetOutputRate(void);
void App_Config_SetOutputRate(uint32_t rate_hz);
uint32_t App_Config_GetUartBaudrate(uint8_t uart_num);
void App_Config_SetUartBaudrate(uint8_t uart_num, uint32_t baudrate);

/* Configuration change notification */
typedef void (*config_change_callback_t)(config_category_t category);
void App_Config_RegisterCallback(config_change_callback_t callback);
void App_Config_UnregisterCallback(void);

#ifdef __cplusplus
}
#endif

#endif /* __APP_CONFIG_H */
